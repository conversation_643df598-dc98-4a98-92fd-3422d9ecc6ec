<div class="modal micromodal-slide" id="modal-1" aria-hidden="false" style="z-index: 9999;">
    <div class="modal__overlay" tabindex="-1" data-micromodal-close>
        <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="width:600px;">
            <header class="modal__header" style="height: 20px; background-color: transparent;">
                <h2 class="modal__title" id="modal-1-title">
                </h2>
                <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
            </header>
            <main class="modal__content" id="modal-1-content">
                <a href="/doc/gnctpk-bildiri-kitabi.pdf?v=32478654" target="_blank" style="width:80vh;">
                        <img alt="" src="/doc/gnctpk-bildiri-popup.jpg">
                    </a>

            </main>
<!--            <footer class="modal__footer text-center">-->
<!--            </footer>-->
        </div>
    </div>
</div>
<script src="https://unpkg.com/micromodal/dist/micromodal.min.js"></script>
<script>
    MicroModal.show('modal-1',
        {
            onShow: modal => console.info(`${modal.id} is shown`), // [1]
            onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        }
    );
</script>
<link rel="stylesheet" href="assets/css/modal.css?v=2">