!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(r){r.ui=r.ui||{},r.ui.version="1.12.1";var n,i=0,h=Array.prototype.slice;r.cleanData=(n=r.cleanData,function(e){for(var t,i,s=0;null!=(i=e[s]);s++)try{(t=r._data(i,"events"))&&t.remove&&r(i).triggerHandler("remove")}catch(e){}n(e)}),r.widget=function(e,i,t){var s,n,a,o={},h=e.split(".")[0],l=h+"-"+(e=e.split(".")[1]);return t||(t=i,i=r.Widget),r.isArray(t)&&(t=r.extend.apply(null,[{}].concat(t))),r.expr[":"][l.toLowerCase()]=function(e){return!!r.data(e,l)},r[h]=r[h]||{},s=r[h][e],n=r[h][e]=function(e,t){if(!this._createWidget)return new n(e,t);arguments.length&&this._createWidget(e,t)},r.extend(n,s,{version:t.version,_proto:r.extend({},t),_childConstructors:[]}),(a=new i).options=r.widget.extend({},a.options),r.each(t,function(t,s){function n(){return i.prototype[t].apply(this,arguments)}function a(e){return i.prototype[t].apply(this,e)}r.isFunction(s)?o[t]=function(){var e,t=this._super,i=this._superApply;return this._super=n,this._superApply=a,e=s.apply(this,arguments),this._super=t,this._superApply=i,e}:o[t]=s}),n.prototype=r.widget.extend(a,{widgetEventPrefix:s&&a.widgetEventPrefix||e},o,{constructor:n,namespace:h,widgetName:e,widgetFullName:l}),s?(r.each(s._childConstructors,function(e,t){var i=t.prototype;r.widget(i.namespace+"."+i.widgetName,n,t._proto)}),delete s._childConstructors):i._childConstructors.push(n),r.widget.bridge(e,n),n},r.widget.extend=function(e){for(var t,i,s=h.call(arguments,1),n=0,a=s.length;n<a;n++)for(t in s[n])i=s[n][t],s[n].hasOwnProperty(t)&&void 0!==i&&(r.isPlainObject(i)?e[t]=r.isPlainObject(e[t])?r.widget.extend({},e[t],i):r.widget.extend({},i):e[t]=i);return e},r.widget.bridge=function(a,t){var o=t.prototype.widgetFullName||a;r.fn[a]=function(i){var e="string"==typeof i,s=h.call(arguments,1),n=this;return e?this.length||"instance"!==i?this.each(function(){var e,t=r.data(this,o);return"instance"===i?(n=t,!1):t?r.isFunction(t[i])&&"_"!==i.charAt(0)?(e=t[i].apply(t,s))!==t&&void 0!==e?(n=e&&e.jquery?n.pushStack(e.get()):e,!1):void 0:r.error("no such method '"+i+"' for "+a+" widget instance"):r.error("cannot call methods on "+a+" prior to initialization; attempted to call method '"+i+"'")}):n=void 0:(s.length&&(i=r.widget.extend.apply(null,[i].concat(s))),this.each(function(){var e=r.data(this,o);e?(e.option(i||{}),e._init&&e._init()):r.data(this,o,new t(i,this))})),n}},r.Widget=function(){},r.Widget._childConstructors=[],r.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(e,t){t=r(t||this.defaultElement||this)[0],this.element=r(t),this.uuid=i++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=r(),this.hoverable=r(),this.focusable=r(),this.classesElementLookup={},t!==this&&(r.data(t,this.widgetFullName,this),this._on(!0,this.element,{remove:function(e){e.target===t&&this.destroy()}}),this.document=r(t.style?t.ownerDocument:t.document||t),this.window=r(this.document[0].defaultView||this.document[0].parentWindow)),this.options=r.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:r.noop,_create:r.noop,_init:r.noop,destroy:function(){var i=this;this._destroy(),r.each(this.classesElementLookup,function(e,t){i._removeClass(t,e)}),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:r.noop,widget:function(){return this.element},option:function(e,t){var i,s,n,a=e;if(0===arguments.length)return r.widget.extend({},this.options);if("string"==typeof e)if(a={},e=(i=e.split(".")).shift(),i.length){for(s=a[e]=r.widget.extend({},this.options[e]),n=0;n<i.length-1;n++)s[i[n]]=s[i[n]]||{},s=s[i[n]];if(e=i.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=t}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];a[e]=t}return this._setOptions(a),this},_setOptions:function(e){for(var t in e)this._setOption(t,e[t]);return this},_setOption:function(e,t){return"classes"===e&&this._setOptionClasses(t),this.options[e]=t,"disabled"===e&&this._setOptionDisabled(t),this},_setOptionClasses:function(e){var t,i,s;for(t in e)s=this.classesElementLookup[t],e[t]!==this.options.classes[t]&&s&&s.length&&(i=r(s.get()),this._removeClass(s,t),i.addClass(this._classes({element:i,keys:t,classes:e,add:!0})))},_setOptionDisabled:function(e){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!e),e&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(n){var a=[],o=this;function e(e,t){for(var i,s=0;s<e.length;s++)i=o.classesElementLookup[e[s]]||r(),i=n.add?r(r.unique(i.get().concat(n.element.get()))):r(i.not(n.element).get()),o.classesElementLookup[e[s]]=i,a.push(e[s]),t&&n.classes[e[s]]&&a.push(n.classes[e[s]])}return n=r.extend({element:this.element,classes:this.options.classes||{}},n),this._on(n.element,{remove:"_untrackClassesElement"}),n.keys&&e(n.keys.match(/\S+/g)||[],!0),n.extra&&e(n.extra.match(/\S+/g)||[]),a.join(" ")},_untrackClassesElement:function(i){var s=this;r.each(s.classesElementLookup,function(e,t){-1!==r.inArray(i.target,t)&&(s.classesElementLookup[e]=r(t.not(i.target).get()))})},_removeClass:function(e,t,i){return this._toggleClass(e,t,i,!1)},_addClass:function(e,t,i){return this._toggleClass(e,t,i,!0)},_toggleClass:function(e,t,i,s){s="boolean"==typeof s?s:i;var n="string"==typeof e||null===e;return(e={extra:n?t:i,keys:n?e:t,element:n?this.element:e,add:s}).element.toggleClass(this._classes(e),s),this},_on:function(n,a,e){var o,h=this;"boolean"!=typeof n&&(e=a,a=n,n=!1),e?(a=o=r(a),this.bindings=this.bindings.add(a)):(e=a,a=this.element,o=this.widget()),r.each(e,function(e,t){function i(){if(n||!0!==h.options.disabled&&!r(this).hasClass("ui-state-disabled"))return("string"==typeof t?h[t]:t).apply(h,arguments)}"string"!=typeof t&&(i.guid=t.guid=t.guid||i.guid||r.guid++);var s;e=(s=e.match(/^([\w:-]*)\s*(.*)$/))[1]+h.eventNamespace;(s=s[2])?o.on(e,s,i):a.on(e,i)})},_off:function(e,t){t=(t||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(t).off(t),this.bindings=r(this.bindings.not(e).get()),this.focusable=r(this.focusable.not(e).get()),this.hoverable=r(this.hoverable.not(e).get())},_delay:function(e,t){var i=this;return setTimeout(function(){return("string"==typeof e?i[e]:e).apply(i,arguments)},t||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(r(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(r(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(r(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(r(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,t,i){var s,n,a=this.options[e];if(i=i||{},(t=r.Event(t)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),t.target=this.element[0],n=t.originalEvent)for(s in n)s in t||(t[s]=n[s]);return this.element.trigger(t,i),!(r.isFunction(a)&&!1===a.apply(this.element[0],[t].concat(i))||t.isDefaultPrevented())}},r.each({show:"fadeIn",hide:"fadeOut"},function(a,o){r.Widget.prototype["_"+a]=function(t,e,i){var s;"string"==typeof e&&(e={effect:e});var n=e?!0!==e&&"number"!=typeof e&&e.effect||o:a;"number"==typeof(e=e||{})&&(e={duration:e}),s=!r.isEmptyObject(e),e.complete=i,e.delay&&t.delay(e.delay),s&&r.effects&&r.effects.effect[n]?t[a](e):n!==a&&t[n]?t[n](e.duration,e.easing,i):t.queue(function(e){r(this)[a](),i&&i.call(t[0]),e()})}}),r.widget,r.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},r.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase());var a=!1;r(document).on("mouseup",function(){a=!1}),r.widget("ui.mouse",{version:"1.12.1",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var t=this;this.element.on("mousedown."+this.widgetName,function(e){return t._mouseDown(e)}).on("click."+this.widgetName,function(e){if(!0===r.data(e.target,t.widgetName+".preventClickEvent"))return r.removeData(e.target,t.widgetName+".preventClickEvent"),e.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!a){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var t=this,i=1===e.which,s=!("string"!=typeof this.options.cancel||!e.target.nodeName)&&r(e.target).closest(this.options.cancel).length;return!(i&&!s&&this._mouseCapture(e))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){t.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===r.data(e.target,this.widgetName+".preventClickEvent")&&r.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(e){return t._mouseMove(e)},this._mouseUpDelegate=function(e){return t._mouseUp(e)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),a=!0))}},_mouseMove:function(e){if(this._mouseMoved){if(r.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button)return this._mouseUp(e);if(!e.which)if(e.originalEvent.altKey||e.originalEvent.ctrlKey||e.originalEvent.metaKey||e.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&r.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,a=!1,e.preventDefault()},_mouseDistanceMet:function(e){return Math.max(Math.abs(this._mouseDownEvent.pageX-e.pageX),Math.abs(this._mouseDownEvent.pageY-e.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),r.widget("ui.slider",r.ui.mouse,{version:"1.12.1",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,t=this.options,i=this.element.find(".ui-slider-handle"),s=[],n=t.values&&t.values.length||1;for(i.length>n&&(i.slice(n).remove(),i=i.slice(0,n)),e=i.length;e<n;e++)s.push("<span tabindex='0'></span>");this.handles=i.add(r(s.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each(function(e){r(this).data("ui-slider-handle-index",e).attr("tabIndex",0)})},_createRange:function(){var e=this.options;e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:r.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=r("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),"min"!==e.range&&"max"!==e.range||this._addClass(this.range,"ui-slider-range-"+e.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(e){var i,s,n,a,t,o,h=this,l=this.options;return!l.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),o={x:e.pageX,y:e.pageY},i=this._normValueFromMouse(o),s=this._valueMax()-this._valueMin()+1,this.handles.each(function(e){var t=Math.abs(i-h.values(e));(t<s||s===t&&(e===h._lastChangedValue||h.values(e)===l.min))&&(s=t,n=r(this),a=e)}),!1!==this._start(e,a)&&(this._mouseSliding=!0,this._handleIndex=a,this._addClass(n,null,"ui-state-active"),n.trigger("focus"),t=n.offset(),o=!r(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=o?{left:0,top:0}:{left:e.pageX-t.left-n.width()/2,top:e.pageY-t.top-n.height()/2-(parseInt(n.css("borderTopWidth"),10)||0)-(parseInt(n.css("borderBottomWidth"),10)||0)+(parseInt(n.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,a,i),this._animateOff=!0))},_mouseStart:function(){return!0},_mouseDrag:function(e){var t={x:e.pageX,y:e.pageY};t=this._normValueFromMouse(t);return this._slide(e,this._handleIndex,t),!1},_mouseStop:function(e){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(e,this._handleIndex),this._change(e,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(e){var t;return 1<(e=(e="horizontal"===this.orientation?(t=this.elementSize.width,e.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(t=this.elementSize.height,e.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)))/t)&&(e=1),e<0&&(e=0),"vertical"===this.orientation&&(e=1-e),t=this._valueMax()-this._valueMin(),t=this._valueMin()+e*t,this._trimAlignValue(t)},_uiHash:function(e,t,i){var s={handle:this.handles[e],handleIndex:e,value:void 0!==t?t:this.value()};return this._hasMultipleValues()&&(s.value=void 0!==t?t:this.values(e),s.values=i||this.values()),s},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(e,t){return this._trigger("start",e,this._uiHash(t))},_slide:function(e,t,i){var s,n=this.value(),a=this.values();this._hasMultipleValues()&&(s=this.values(t?0:1),n=this.values(t),2===this.options.values.length&&!0===this.options.range&&(i=0===t?Math.min(s,i):Math.max(s,i)),a[t]=i),i!==n&&!1!==this._trigger("slide",e,this._uiHash(t,i,a))&&(this._hasMultipleValues()?this.values(t,i):this.value(i))},_stop:function(e,t){this._trigger("stop",e,this._uiHash(t))},_change:function(e,t){this._keySliding||this._mouseSliding||(this._lastChangedValue=t,this._trigger("change",e,this._uiHash(t)))},value:function(e){return arguments.length?(this.options.value=this._trimAlignValue(e),this._refreshValue(),void this._change(null,0)):this._value()},values:function(e,t){var i,s,n;if(1<arguments.length)return this.options.values[e]=this._trimAlignValue(t),this._refreshValue(),void this._change(null,e);if(!arguments.length)return this._values();if(!r.isArray(e))return this._hasMultipleValues()?this._values(e):this.value();for(i=this.options.values,s=e,n=0;n<i.length;n+=1)i[n]=this._trimAlignValue(s[n]),this._change(null,n);this._refreshValue()},_setOption:function(e,t){var i,s=0;switch("range"===e&&!0===this.options.range&&("min"===t?(this.options.value=this._values(0),this.options.values=null):"max"===t&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),r.isArray(this.options.values)&&(s=this.options.values.length),this._super(e,t),e){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(t),this.handles.css("horizontal"===t?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),i=s-1;0<=i;i--)this._change(null,i);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(e){this._super(e),this._toggleClass(null,"ui-state-disabled",!!e)},_value:function(){var e=this.options.value;return this._trimAlignValue(e)},_values:function(e){var t,i,s;if(arguments.length)return t=this.options.values[e],this._trimAlignValue(t);if(this._hasMultipleValues()){for(i=this.options.values.slice(),s=0;s<i.length;s+=1)i[s]=this._trimAlignValue(i[s]);return i}return[]},_trimAlignValue:function(e){if(e<=this._valueMin())return this._valueMin();if(e>=this._valueMax())return this._valueMax();var t=0<this.options.step?this.options.step:1,i=(e-this._valueMin())%t;e-=i;return 2*Math.abs(i)>=t&&(e+=0<i?t:-t),parseFloat(e.toFixed(5))},_calculateNewMax:function(){var e=this.options.max,t=this._valueMin(),i=this.options.step;(e=Math.round((e-t)/i)*i+t)>this.options.max&&(e-=i),this.max=parseFloat(e.toFixed(this._precision()))},_precision:function(){var e=this._precisionOf(this.options.step);return null!==this.options.min&&(e=Math.max(e,this._precisionOf(this.options.min))),e},_precisionOf:function(e){var t=e.toString();return-1===(e=t.indexOf("."))?0:t.length-e-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(e){"vertical"===e&&this.range.css({width:"",left:""}),"horizontal"===e&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var t,i,e,s,n,a=this.options.range,o=this.options,h=this,l=!this._animateOff&&o.animate,u={};this._hasMultipleValues()?this.handles.each(function(e){i=(h.values(e)-h._valueMin())/(h._valueMax()-h._valueMin())*100,u["horizontal"===h.orientation?"left":"bottom"]=i+"%",r(this).stop(1,1)[l?"animate":"css"](u,o.animate),!0===h.options.range&&("horizontal"===h.orientation?(0===e&&h.range.stop(1,1)[l?"animate":"css"]({left:i+"%"},o.animate),1===e&&h.range[l?"animate":"css"]({width:i-t+"%"},{queue:!1,duration:o.animate})):(0===e&&h.range.stop(1,1)[l?"animate":"css"]({bottom:i+"%"},o.animate),1===e&&h.range[l?"animate":"css"]({height:i-t+"%"},{queue:!1,duration:o.animate}))),t=i}):(e=this.value(),s=this._valueMin(),n=this._valueMax(),i=n!==s?(e-s)/(n-s)*100:0,u["horizontal"===this.orientation?"left":"bottom"]=i+"%",this.handle.stop(1,1)[l?"animate":"css"](u,o.animate),"min"===a&&"horizontal"===this.orientation&&this.range.stop(1,1)[l?"animate":"css"]({width:i+"%"},o.animate),"max"===a&&"horizontal"===this.orientation&&this.range.stop(1,1)[l?"animate":"css"]({width:100-i+"%"},o.animate),"min"===a&&"vertical"===this.orientation&&this.range.stop(1,1)[l?"animate":"css"]({height:i+"%"},o.animate),"max"===a&&"vertical"===this.orientation&&this.range.stop(1,1)[l?"animate":"css"]({height:100-i+"%"},o.animate))},_handleEvents:{keydown:function(e){var t,i,s,n=r(e.target).data("ui-slider-handle-index");switch(e.keyCode){case r.ui.keyCode.HOME:case r.ui.keyCode.END:case r.ui.keyCode.PAGE_UP:case r.ui.keyCode.PAGE_DOWN:case r.ui.keyCode.UP:case r.ui.keyCode.RIGHT:case r.ui.keyCode.DOWN:case r.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,this._addClass(r(e.target),null,"ui-state-active"),!1===this._start(e,n)))return}switch(s=this.options.step,t=i=this._hasMultipleValues()?this.values(n):this.value(),e.keyCode){case r.ui.keyCode.HOME:i=this._valueMin();break;case r.ui.keyCode.END:i=this._valueMax();break;case r.ui.keyCode.PAGE_UP:i=this._trimAlignValue(t+(this._valueMax()-this._valueMin())/this.numPages);break;case r.ui.keyCode.PAGE_DOWN:i=this._trimAlignValue(t-(this._valueMax()-this._valueMin())/this.numPages);break;case r.ui.keyCode.UP:case r.ui.keyCode.RIGHT:if(t===this._valueMax())return;i=this._trimAlignValue(t+s);break;case r.ui.keyCode.DOWN:case r.ui.keyCode.LEFT:if(t===this._valueMin())return;i=this._trimAlignValue(t-s)}this._slide(e,n,i)},keyup:function(e){var t=r(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,t),this._change(e,t),this._removeClass(r(e.target),null,"ui-state-active"))}}})});