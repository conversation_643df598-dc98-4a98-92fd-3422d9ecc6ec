:root {
  // --theme-color         : #fe5d37;
  --theme-color         : #9C29B2;
  --secondary-color     : #88c90d;
  --title-color         : #103741;
  --body-color          : #74787c;
  --smoke-color         : #f9f6ef;
  // --smoke-theme         : #ffe9e4;
  --smoke-theme         : #fef5ff;
  // --light-color         : #888888;
  --light-color         : #9b9b9b;
  --black-color         : #000000;
  --white-color         : #ffffff;
  --yellow-color        : #fec624;
  --success-color       : #28a745;
  --error-color         : #dc3545;
  --blue-color          : #786acf;
  --border-color        : rgb(238, 241, 242);
  --title-font          : 'Baloo 2', cursive;
  --para-font           : 'Catamaran', sans-serif;
  --body-font           : 'Baloo 2', cursive;
  --icon-font           : 'Font Awesome 6 Pro';
  --main-container      : 1220px;
  --container-gutters   : 30px;
  --section-space       : 120px;
  --section-space-mobile: 80px;
  --section-title-space : 60px;
  --ripple-ani-duration : 5s;
}


// Color Variation
$theme-color          : var(--theme-color);
$secondary-color      : var(--secondary-color);
$title-color          : var(--title-color);
$body-color           : var(--body-color);
$smoke-color          : var(--smoke-color);
$smoke-theme          : var(--smoke-theme);
$light-color          : var(--light-color);
$white-color          : var(--white-color);
$black-color          : var(--black-color);
$yellow-color         : var(--yellow-color);
$success-color        : var(--success-color);
$blue-color           : var(--blue-color);
$error-color          : var(--error-color);
$border-color         : var(--border-color);

// Font Variation
$icon-font   : var(--icon-font);

// Typography
$title-font      : var(--title-font);
$body-font       : var(--body-font);
$para-font       : var(--para-font);
$body-font-size  : 16px;
$body-line-Height: 26px;
$body-font-weight: 400;
$p-line-Height   : 1.625;


// Device Variation
$hd: 1921px; // Large Device Than 1920
$xxl: 1680px; // Extra large Device
$xl: 1500px; // Extra large Device
$ml: 1399px; // Medium Large Device
$lg: 1199px; // Large Device (Laptop)
$md: 991px; // Medium Device (Tablet)
$sm: 767px; // Small Device
$xs: 575px; // Extra Small Device


// Spacing Count with 5x
$space-count: 10;

// Section Space  For large Device
$space         : var(--section-space);
$space-extra   : calc(var(--section-space) - 30px);

// Section Space On small Device
$space-mobile         : var(--section-space-mobile);
$space-mobile-extra   : calc(var(--section-space-mobile) - 30px);


// BG Color Mapping 
$bgcolorMap    : ();
$bgcolorMap    : map-merge((
  "theme"      : $theme-color,
  "smoke"      : $smoke-color,
  "smoke-theme": $smoke-theme,
  "white"      : $white-color,
  "black"      : $black-color,
  "title"      : $title-color,
), $bgcolorMap);


// Overlay Color Mapping 
$overlaycolorMap : ();
$overlaycolorMap : map-merge((
  "theme"        : $theme-color,
  "secondary"    : $secondary-color,
  "title"        : $title-color,
  "white"        : $white-color,
  "black"        : $black-color,
  "custom"       : #03252d,
), $overlaycolorMap);


// Text Color Mapping 
$textColorsMap : ();
$textColorsMap : map-merge((
  "theme"      : $theme-color,
  "title"      : $title-color,
  "body"       : $body-color,
  "light"      : $light-color,
  "white"      : $white-color,
  "yellow"     : $yellow-color,
  "success"    : $success-color,
  "blue"       : $blue-color,
  "error"      : $error-color), 
$textColorsMap);


// Font Mapping 
$fontsMap    : ();
$fontsMap    : map-merge((
  "icon"     : $icon-font,
  "title"    : $title-font,
  "para"     : $para-font,
  "body"     : $body-font,
), $fontsMap);