.footer-widget {
  margin-bottom: 40px;

  &,
  .widget {
    padding: 0;
    border: none;
    padding-bottom: 0;
    background-color: transparent;
  }

  .widget_title {
    position: relative;
    border-left: none;
    font-family: $title-font;
    font-size: 24px;
    margin-top: -0.15em;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: none;
    font-weight: bold;
    position: relative;

    &:after {
      content: "";
      height: 1px;
      width: 50px;
      background-color: $title-color;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }


  &.widget_meta,
  &.widget_pages,
  &.widget_archive,
  &.widget_categories,
  &.widget_nav_menu {
    margin-bottom: 45px;

    ul {
      margin-top: 0;
    }

    .widget_title {
      margin-bottom: 25px;
    }

    a {
      background-color: transparent;
      padding: 0;
      position: relative;
      margin-bottom: 18px;
      display: inline-block;
      font-size: 16px;
      font-weight: 400;
      border: none;
      transition: all ease 0.4s;

      &:before {
        content: "\f105";
        font-family: $icon-font;
        margin-right: 10px;
        font-weight: 300;
        color: $body-color;
        transition: all ease 0.4s;
      }

      &:hover {
        background-color: transparent;
        color: $theme-color;

        &::before {
          color: $theme-color;
        }
      }
    }

    li {
      >span {
        @include equal-size(auto);
        position: relative;
        background-color: transparent;
        color: $body-color;
        line-height: 1;
      }

      &:last-child {
        a {
          margin-bottom: 0;
        }
      }
    }
  }

  .about-text {
    margin-top: -14px;
  }

  .recent-post {
    max-width: 280px;
  }

  .vs-widget-contact {
    max-width: 240px;
    margin-top: -4px;
  }
}

.footer-layout2 {
  .footer-widget {
    margin-bottom: 40px;

    .widget_title {
      color: $white-color;

      &:after {
        background-color: $theme-color;
        height: 2px;
      }
    }

    p {
      color: $light-color;
    }

    &.widget_meta,
    &.widget_pages,
    &.widget_archive,
    &.widget_categories,
    &.widget_nav_menu {
      margin-bottom: 45px;

      a {
        color: $light-color;

        &:hover {
          color: $secondary-color;

          &:before {
            color: $secondary-color;
          }
        }
      }
    }

    .recent-post {
      .media-img {
        border-radius: 15px;
        overflow: hidden;

        img {
          border-radius: 15px;
        }
      }

      .recent-post-meta {
        a {
          &:hover {
            color: $secondary-color;
          }
        }
      }

      .post-title {
        color: $smoke-color;

        a {
          color: inherit;

          &:hover {
            color: $secondary-color;
          }
        }
      }
    }
  }
}

.vs-widget-about {
  max-width: 270px;
}

@include lg {}

@include md {
  .footer-widget {
    .widget_title {
      margin-bottom: 18px;
    }
  }
}

@include sm {
  .vs-widget-about {
    text-align: left;
    margin: unset;
    max-width: 450px;
  }
}