.widget {
  --border-color: rgb(242, 242, 242);
  padding: 40px;
  margin-bottom: 40px;

  select,
  input {
    color: $title-color;
    padding-left: 30px;
    font-size: 16px;
    background-color: $smoke-color;
    border: none;
    height: 60px;
    border-radius: 50px;
    font-weight: 500;
    width: 100%;

    @include inputPlaceholder {
      color: $title-color;
      opacity: 1;
    }
  }
}

.widget_title {
  font-size: 24px;
  line-height: 1em;
  margin-bottom: 25px;
  margin-top: -0.1em;
  font-weight: 700;
  position: relative;
  padding-bottom: 15px;

  &::after {
    content: '';
    width: 60px;
    height: 2px;
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: $theme-color;
  }
}

.widget {
  .search-form {
    position: relative;

    input {
      padding-right: 50px;
    }

    button {
      text-align: center;
      padding: 0;
      color: $title-color;
      background-color: transparent;
      border: none;
      position: absolute;
      right: 30px;
      line-height: 1;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: $theme-color;
      }
    }
  }
}

.wp-block-tag-cloud,
.tagcloud {
  a {
    display: inline-block;
    border: none;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    padding: 9.5px 23px;
    margin-right: 5px;
    margin-bottom: 10px;
    color: var(--theme-color);
    background-color: var(--tag-bg, $smoke-theme);
    border-radius: 9999px;
    font-weight: 400;

    &:hover {
      background-color: $theme-color;
      color: $white-color !important;
      box-shadow: none;
    }
  }
}

.tagcloud {
  margin-right: -5px;
  margin-bottom: -10px;
}

.widget {
  .tagcloud {
    a:not(:hover) {
      color: $body-color;
      background-color: $white-color;
      box-shadow: 0px 5px 15px 0px rgba(0, 19, 87, 0.06);
    }
  }
}

.widget {
  &.widget_banner {
    padding: 0;

    .content {
      text-align: center;
      position: absolute;
      width: 100%;
      top: 55px;
      left: 0;
    }

    .banner-btn {
      position: absolute;
      bottom: 60px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }

  .post-title {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 5px;
    color: $title-color;
    line-height: 24px;

    a {
      color: inherit;

      &:hover {
        color: $theme-color;
      }
    }
  }

  .media-img {
    margin-right: 17px;
    width: 95px;
    overflow: hidden;

    img {
      transition: all ease 0.4s;
      transform: scale(1);
    }
  }

  .recent-post-meta {
    line-height: 1.8;
    margin-bottom: 3px;

    a {
      text-transform: capitalize;
      margin-right: 15px;
      font-size: 14px;
      color: #8f959b;

      // color: $theme-color;
      &:hover {
        color: $theme-color;
      }

      i {
        margin-right: 5px;
        font-size: 12px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &:hover {
    .media-img {
      img {
        transform: scale(1.13);
        @include safariNoScale();
      }
    }
  }
}

.about-logo img {
  max-width: 220px;
}

.sidebar-area {
  ul.wp-block-latest-posts {
    margin-bottom: 0;

    li {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .widget {
    border: 2px solid var(--border-color);
    position: relative;
    background-color: $white-color;
    border-radius: 30px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .recent-post {
    border-bottom: 1px solid #ecedf0;
    padding-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    .media-img {
      margin-right: 20px;
      width: 90px;
      height: 80px;
      border-radius: 20px;
      overflow: hidden;

      img {
        border-radius: 20px;
      }
    }

    .post-title {
      font-weight: 700;
      font-size: 16px;
      margin-bottom: 0;
      color: var(--title-color);
      line-height: 20px;
    }

    .recent-post-meta a {
      font-size: 14px;
    }
  }

  .bg {
    border-radius: 20px;
    background-color: rgb(227, 227, 227);
    position: absolute;
    left: 1262px;
    top: 1523px;
    width: 90px;
    height: 80px;
    z-index: 127;
  }

  .widget .wp-block-search {
    margin-bottom: 0;
  }

  .wp-block-group__inner-container h2 {
    font-size: 20px;
    line-height: 1em;
    margin-bottom: 20px;
    margin-top: -0.07em;
  }

  ol.wp-block-latest-comments {
    padding: 0;
    margin: 0;

    li {
      line-height: 1.5;
      margin: 0 0 20px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      padding-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }
    }
  }
}

@include lg {
  .widget {
    padding: 33px 30px;
  }
}

@include md {

  .wp-block-tag-cloud a,
  .tagcloud a {
    padding: 10.5px 18px;
  }

  .sidebar-area {
    padding-top: 30px;
  }
}

@include sm {
  .widget {
    padding: 30px 25px;
  }
}

@include xs {
  .widget {
    padding: 30px 20px;
  }

  .widget.widget_banner {
    .content {
      top: 25px;
    }

    .banner-btn {
      bottom: 30px;
    }
  }
}