.vs-pagination {
  text-align: center;

  ul {
    margin: 0;
    padding: 0;
  }

  li {
    display: inline-block;
    margin: 0 3px;
    list-style-type: none;

    &:last-child {
      margin-right: 0;
    }

    &:first-child {
      margin-left: 0;
    }
  }

  span,
  a {
    display: inline-block;
    width: 55px;
    height: 55px;
    line-height: 54px;
    text-align: center;
    position: relative;
    background-color: #fff;
    z-index: 1;
    font-weight: 600;
    border-radius: 50%;
    color: $theme-color;
    font-size: 20px;
    border: 1px solid $theme-color;
    font-weight: 700;

    i {
      position: relative;
      top: 0.06em;
      font-size: 16px;
      line-height: 1;
    }

    &.active,
    &:hover {
      color: $white-color;
      background-color: $theme-color;
      border-color: transparent;
    }
  }

}

@include sm {
  .vs-pagination {

    span,
    a {
      @include equal-size-lineHeight(40px);
      font-size: 14px;

      i {
        font-size: 12px
      }
    }
  }
}


@include vxs {
  .vs-pagination {

    span,
    a {
      @include equal-size-lineHeight(30px);
    }
  }
}