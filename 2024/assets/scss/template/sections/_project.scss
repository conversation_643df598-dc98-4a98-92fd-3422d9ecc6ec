.project-card {
    position: relative;
    border-radius: 30px;
    overflow: hidden;

    .project-img {
        position: relative;

        img {
            transform: scale(1.001);
            transition: all ease 0.6s;
            border-radius: 30px;
        }

        &:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            @include equal-size(100%);
            background-color: $secondary-color;
            transition: 0.6s ease-in-out;
            transform: scale(0);
            border-radius: 30px;
            opacity: 0;
            visibility: hidden;
            z-index: 1;
        }
    }
    
    .plus-btn {
        font-size: 72px;
        color: $white-color;
        position: absolute;
        top: 50%;
        left: 50%;
        visibility: hidden;
        opacity: 0;
        transition: 0.3s;
        z-index: 2;
        transition-delay: 0.3s;
        transform: translate(-50%, -50%) scale(0);

        &:hover {
            color: $theme-color;
        }
    }

    &:hover {
        .project-img {
            img {
                transform: scale(1.07);
                @include safariNoScale();
            }
            
            &:before {
                transform: scale(1);
                opacity: 0.75;
                visibility: visible;
                transform: scale(1);
            }
        }

        .plus-btn {
            visibility: visible;
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
}

// Shape btn
.project-menu {
    button {
        border: none;
        background-color: transparent;
        font-size: 18px;
        font-weight: 600;
        color: $title-color;
        min-width: 180px;
        height: 74px;
        padding: 20px 50px;
        margin: 0 10px;
        transition: 0.3s ease-in-out;
        position: relative;
        z-index: 2;

        img {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            z-index: -1;
            transition: 0.3s;
        }

        .btn-active-bg {
            visibility: hidden;
            opacity: 0;
            z-index: -1;
            transition: 0.3s;
        }

        &:hover,
        &.active {
            .btn-active-bg {
                visibility: visible;
                opacity: 1;
            }
        }
    }
}


// project-section-two
.project-section-two{
    .paret{
        position: absolute;
        top: 130px;
        left: 0;
        @include lg {
            display: none !important;
        }
    }
    .flayer{
        position: absolute;
        top: 215px;
        right: 103px;
        @include lg {
            display: none !important;
        }
    }
    .rocket{
        position: absolute;
        bottom: 60px;
        right: 130px;
        @include lg {
            display: none !important;
        }
    }
    .tube {
        position: absolute;
        bottom: 106px;
        left: 169px;
        @include lg {
            display: none !important;
        }
    }

    // Shape btn
    .project-menu {
        @include md{
            text-align: left !important;
            margin-bottom: 20px;
        }
        button {
            border: none;
            background-color: var(--white-color);
            font-size: 18px;
            font-weight: 600;
            color: var(--theme-color);
            padding: 0px 30px;
            min-width: unset;
            margin: 0 10px;
            height: 50px;
            line-height: 50px;
            transition: 0.3s ease-in-out;
            position: relative;
            border-radius: 50px;
            z-index: 2;
            &:hover,
            &.active {
                background-color: var(--theme-color);
                color: var(--white-color);
            }
            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                z-index: -1;
                transition: 0.3s;
            }
            .btn-active-bg {
                visibility: hidden;
                opacity: 0;
                z-index: -1;
                transition: 0.3s;
            }
            @include md{
                margin-bottom: 20px;
            }
        }
    }
}

// project-card-two
.project-card-two {
    position: relative;
    border-radius: 30px;
    overflow: hidden;

    .project-img {
        position: relative;

        img {
            transform: scale(1.001);
            transition: all ease 0.6s;
            border-radius: 30px;
            width: 100%;
        }

        &:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            @include equal-size(100%);
            background-color: $secondary-color;
            transition: 0.6s ease-in-out;
            transform: scale(0);
            border-radius: 30px;
            opacity: 0;
            visibility: hidden;
            z-index: 1;
        }
    }
    
    .plus-btn {
        font-size: 24px;
        width: 60px;
        height: 60px;
        line-height: 65px;
        border-radius: 10px;
        text-align: center;
        background-color: var(--white-color);
        color: $secondary-color;
        position: absolute;
        top: 50%;
        left: 50%;
        visibility: hidden;
        opacity: 0;
        transition: 0.3s;
        z-index: 2;
        transition-delay: 0.3s;
        transform: translate(-50%, -50%) scale(0);

        &:hover {
            color: $theme-color;
        }
    }

    &:hover {
        .project-img {
            img {
                transform: scale(1.07);
                @include safariNoScale();
            }
            
            &:before {
                transform: scale(1);
                opacity: 0.75;
                visibility: visible;
                transform: scale(1);
            }
        }

        .plus-btn {
            visibility: visible;
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
}

@include md {
    .project-menu {
        button {
            font-size: 16px;
            min-width: 150px;
            height: 60px;
            padding: 18px 30px;
            margin: 0 4px;
        }
    }
}

@include sm {
    .project-menu {
        button {
            margin: 6px 4px;
        }
    }
}