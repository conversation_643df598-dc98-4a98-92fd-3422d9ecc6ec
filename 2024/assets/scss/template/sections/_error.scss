.vs-error-wrapper {
  position: relative;
  min-height: 100vh;
  background-color: $title-color;
}

.error-shape {
  position: absolute !important;
  top: 12%;
  right: 15%;
  max-width: 630px;
}

.error-content {
  max-width: 570px;
  padding-top: 254px;
  padding-bottom: 254px;
}

.error-title {
  font-size: 80px;
  margin-bottom: 10px;
}

.error-text {
  font-size: 24px;
  margin-bottom: 48px;
}

.error-number {
  // color: rgba(181, 191, 255, 0.05);
  color: $white-color;
  display: block;
  margin-bottom: 0;
  line-height: 1;
}

@include ml {
  .error-content {
    max-width: 480px;
  }

  .error-title {
    font-size: 64px;
  }

  .error-shape {
    max-width: 520px;
  }

  .error-text {
    font-size: 20px;
    margin-bottom: 30px;
  }
}

@include lg {
  .vs-error-wrapper {
    position: relative;
    min-height: 500px;
  }

  .error-shape {
    max-width: 440px;
    top: 20%;
    right: 5%;
  }
}

@include md {
  .error-shape {
    display: none !important;
  }

  .error-content {
    text-align: center;
    max-width: 460px;
    margin-left: auto;
    margin-right: auto;
  }

  .error-title {
    font-size: 48px;
  }

  .error-text {
    font-size: 18px;
  }

  .error-shape {
    display: none;
  }
}

@include xs {
  .error-title {
    font-size: 36px;
  }

  .error-text {
    font-size: 16px;
  }
}