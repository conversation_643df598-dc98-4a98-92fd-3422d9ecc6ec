// Comment ----------------------------
.vs-comment-wrap {
  .blog-inner-title {
    margin-bottom: 0;
  }
}

ul.comment-list,
.children {
  list-style: none;
  margin: 0;
  padding: 0;
}

.vs-post-comment {
  --border-color: rgb(241, 240, 239);
  padding: 40px 0 34px 0;
  transition: .4s;
  border-bottom: 1px solid $border-color;

  .comment-avater {
    width: 100px;
    height: 100px;
    margin-right: 30px;
    overflow: hidden;
    border-radius: 50%;
    float: left;

    img {
      border-radius: 50%;
    }
  }

  .comment-content {
    position: relative;
    line-height: 1;
    padding-left: 130px;
  }

  .name {
    margin-bottom: 15px;
    margin-top: -5px;
    font-size: 20px;
    font-weight: 600;
  }

  .commented-on {
    font-size: 14px;
    display: block;
    font-weight: 400;
    margin-bottom: 10px;
    font-family: $para-font;
    color: $theme-color;
    text-transform: uppercase;
  }

  .reply_and_edit,
  .star-rating {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
  }

  .star-rating {
    width: 92px;
  }

  .reply-btn {
    color: $theme-color;
    min-width: 112px;
    font-family: $body-font;
    padding: 0 20px;
    height: 40px;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;

    &:before {
      content: "\f3e5";
      font-family: $icon-font;
      font-weight: 600;
      margin-right: 5px;
    }

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }

  .text {
    margin-bottom: 0;
  }
}

.children {
  .vs-post-comment {
    padding-left: 70px;
  }
}

ul.comment-list {
  .vs-comment:first-child:not(.children .vs-comment) {
    >.vs-post-comment {
      padding-top: 30px;
    }
  }

  .vs-comment:last-child:not(.children .vs-comment) {
    >.vs-post-comment {
      border-bottom: none;
    }
  }
}

// Comment form ----------------------
.vs-comment-form,
.vs-register-form {
  background-color: $white-color;
  border: 2px solid rgba(242, 242, 242, 1);
  padding: 60px;
  margin-top: 15px;
  border-radius: 30px;

  .row {
    --bs-gutter-x: 20px;
  }

  .form-group {
    &:last-child {
      margin-top: 10px;
    }
  }

  .blog-inner-title {
    margin-bottom: 0;
  }

  .form-text {
    margin-bottom: 25px;
  }
}

.form-text {
  font-size: 1em;
}

.vs-register-form {
  margin-top: 40px;

  .form-title {
    margin-bottom: 30px;
  }
}


.vs-comment-form {

  input,
  select {
    height: 60px;
  }

  select.style2~i,
  .form-select.style2~i,
  .form-control.style2~i {
    top: 17.5px;
    font-size: 16px;
  }

}

@include lg {

  .vs-comment-form,
  .vs-register-form {
    padding: 30px;
  }
}

@include md {

  .vs-comment-form,
  .vs-register-form {
    margin-bottom: 30px;
  }
}

@include sm {
  .vs-post-comment {
    padding: 25px 0 20px 0;

    .comment-avater {
      float: none;
    }

    .comment-content {
      padding-left: 0;
      margin-top: 25px;
    }
  }

  .children .vs-post-comment {
    padding-left: 50px;
  }

  .vs-comment-form,
  .vs-register-form {
    margin-top: 20px;
    padding: 25px;

    .mb-40 {
      margin-bottom: 20px;
    }
  }

  .form-title .form-text {
    font-size: 14px;
    line-height: 20px;
    margin-top: 10px;
  }
}

@include xs {
  .vs-post-comment {
    flex-direction: column;

    .comment-avater {
      margin-right: 20px;
      margin-bottom: 20px;
    }

    .comment-content {
      width: 100%;
    }
  }
}