<!doctype html>
<html class="no-js" lang="TR">

<head>
    <title>9. Genç Pediatristler Kongresi</title>
    <?php include('head.php') ?>
</head>

<body>
    <?php include('header.php') ?>


    <!--======== Hero Section ========-->
    <section class="vs-hero-wrapper hero-3 position-relative">
        <div class="section-after style-2 d-none d-md-block">
            <img src="assets/img/shape/hero3-after.png" alt="shape">
        </div>
        <div class="hero-slider3 vs-carousel" data-slide-show="1" data-md-slide-show="1" data-arrows="true" data-fade="true">
            <!-- Single Slide -->
            <div class="vs-hero-inner">
                <div class="vs-hero-bg">
                    <img src="doc/slider2.jpg" alt="overlay">
                </div>
                <div class="hero-content3">
                    <img src="doc/9genctpk_2ok5.png" alt="overlay">
                </div>
            </div>


        </div> <!-- / Slider end -->
        <div class="shape-mockup rotate d-none d-md-block" data-top="10%" data-left="11%"><img src="assets/img/icon/sun-4.png" alt="shapes"></div>
        <!--
        <div class="shape-mockup movingBottomLeft" data-bottom="20%" data-left="0%"><img src="assets/img/icon/car-5.png"
                alt="shapes"></div>
        <div class="shape-mockup movingTopLeft d-none d-md-block" data-bottom="3%" data-right="3%"><img
                src="assets/img/icon/rain-2.png" alt="shapes"></div>
        <div class="shape-mockup moving d-none d-xl-block" data-top="9%" data-right="8%"><img
                src="assets/img/icon/m.png" alt="shapes"></div>
            -->
    </section>

    <section class="service-section space">
        <div class="container">

            <div class="row gy-30">
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 service-card wow fadeInUp" data-wow-delay="0.1s">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="kurullar.php">Kurullar</a></h2>
                        <p class="sr-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </p>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 service-card wow fadeInUp" data-wow-delay="0.1s">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="genel-bilgiler.php">Genel Bilgiler</a></h2>
                        <p class="sr-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </p>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 service-card wow fadeInUp" data-wow-delay="0.1s">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="kayit-konaklama.php">Kayıt & Konaklama</a></h2>
                        <p class="sr-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </p>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 service-card wow fadeInUp" data-wow-delay="0.1s">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="iletisim.php">İletişim</a></h2>
                        <p class="sr-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="shape-mockup z-index-3 d-none d-xl-block" data-top="-4%" data-left="4%"><img src="assets/img/shape/service-line.png" alt="shapes"></div>

    </section>

    <!--======== Counter Section ========-->
    <section class="counter-section space-double" data-bg-src="assets/img/bg/counter.jpg" data-overlay="title" data-opacity="8">
        <div class="container">
            <!-- <div class="row text-center justify-content-center wow fadeInUp" style="visibility: visible; animation-delay: 0.1s; animation-name: fadeInUp;">
                <div class="col-xl-6 col-lg-7 col-md-8 col-sm-9">
                    <div class="title-area">
                        <h2 class="sec-title" style="color: #fff">Kongreye Kalan Süre</h2>
                    </div>
                </div>
            </div> -->
            <div class="row gy-30 justify-content-between">
                <div class="col-xl-auto col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="counter-box">
                        <div class="counter-info text-center">
                            <h2 id="days" class="counter-number"></h2>
                        </div>
                    </div>
                </div>
                <div class="col-xl-auto col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="counter-box">
                        <div class="counter-info text-center">
                            <h2 id="hours" class="counter-number"></h2>
                        </div>
                    </div>
                </div>
                <div class="col-xl-auto col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="counter-box">
                        <div class="counter-info text-center">
                            <h2 id="minutes" class="counter-number"></h2>
                        </div>
                    </div>
                </div>
                <div class="col-xl-auto col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="counter-box">
                        <div class="counter-info text-center">
                            <h2 id="seconds" class="counter-number"></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="section-before style-2"><img src="assets/img/shape/counter-before-1.png" alt="shape"></div>
        <div class="section-after style-2"><img src="assets/img/shape/counter-after-1.png" alt="shape"></div>

        <div class="shape-mockup z-index-3 d-none d-hd-block" data-bottom="-9%" data-right="7%"><img src="assets/img/shape/line-3.png" alt="shapes"></div>
    </section>


    <!--======== / Counter Section ========-->

    <!--======== About Section ========-->
    <section class="about-section space-top pb-30 mb-80">
        <div class="container">
            <div class="row gy-30 align-items-center justify-content-center">
                <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.1s">
                    <span class="sub-title">9. Genç Pediatristler Kongresi</span>
                    <h2 class="sec-title big-title">Davet</h2>
                    <p class="fs-md"><strong>Değerli meslektaşlarım,</strong></p>
                    <p class="fs-md">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris vulputate justo id elit
                        dignissim bibendum. Curabitur et volutpat tellus, ut auctor magna. Cras eu eleifend orci.
                        Suspendisse rutrum pretium erat, id suscipit odio rhoncus eu. Class aptent taciti sociosqu ad
                        litora torquent per conubia nostra, per inceptos himenaeos. Quisque et laoreet magna, at blandit
                        dolor. Aenean condimentum, tortor ac accumsan varius, ligula purus posuere nulla, et cursus
                        tellus tellus id tellus. Orci varius natoque penatibus et magnis dis parturient montes, nascetur
                        ridiculus mus.</p>



                </div>
            </div>

        </div>
    </section>
    <!--======== / About Section ========-->

    <?php include('footer.php') ?>

    <!-- Display the countdown timer in an element -->
    <script>
        // Set the date we're counting down to
        var countDownDate = new Date("Nov 5, 2024 15:37:25").getTime();

        // Update the count down every 1 second
        var x = setInterval(function() {

            // Get today's date and time
            var now = new Date().getTime();

            // Find the distance between now and the count down date
            var distance = countDownDate - now;

            // Time calculations for days, hours, minutes and seconds
            var days = Math.floor(distance / (1000 * 60 * 60 * 24));
            var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Display the result in the element with id="demo"
            $("#days").html(days + "<br><span>Gün</span>");
            $("#hours").html(hours + "<br><span>Saat</span>");
            $("#minutes").html(minutes + "<br><span>Dakika</span>");
            $("#seconds").html(seconds + "<br><span>Saniye</span>");

            // If the count down is finished, write some text
            if (distance < 0) {
                clearInterval(x);
                document.getElementById("demo").innerHTML = "EXPIRED";
            }
        }, 1000);
    </script>

    <?php include('script.php') ?>

</body>

</html>