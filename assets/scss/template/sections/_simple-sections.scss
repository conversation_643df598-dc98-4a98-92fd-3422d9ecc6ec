.img-box-1 {
  img {
    border-radius: 30px;
  }
}

.img-box-2 {
  border-radius: 50%;
  position: relative;
  @include equal-size(558px);
  border: 2px dashed rgb(255, 228, 222);

  img {
    border-radius: 50%;
  }

  .small-img {
    @include equal-size(250px);
    position: absolute;
    bottom: 30px;
    right: 0;
  }

  .big-img {
    position: absolute;
    top: -7px;
    left: -7px;
    max-width: calc(100% - 46px);
  }
}

.feature-box {
  display: flex;

  .feature-icon {
    min-width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    margin-right: 20px;
    background-color: $white-color;
    box-shadow: 0px 9px 46px 0px rgba(75, 83, 100, 0.14);
    border-radius: 50%;
    display: inline-block;
    transition: all ease 0.4s;

    img {
      transition: all ease 0.4s;
      filter: none;
    }
  }

  .feature-info {
    margin-bottom: 35px;
  }

  &:last-child {
    .feature-info {
      margin-bottom: 0;
    }
  }

  .feature-title {
    margin-top: -0.25rem;
    margin-bottom: 7px;
  }

  .feature-text {
    margin-bottom: 0;
  }

  &:hover {
    .feature-icon {
      background-color: $theme-color;
      color: $white-color;
      box-shadow: none;

      img {
        filter: brightness(0) invert(1);
      }
    }
  }
}

// simple-section
.simple-section {
  position: relative;
  padding-left: 120px;

  @include xl {
    padding-left: 15px;
  }

  @include lg {
    padding: 0 15px;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 945px;
    height: 540px;
    background: #DBEAF7;
    z-index: -11;
  }

  .img-box-1 {
    margin-right: -36px;

    @include lg {
      margin-right: 0;
      margin-bottom: 30px;
    }

    img {
      width: 100%;
    }
  }

  .content-box {
    position: relative;
    border-radius: 30px 0px 0px 30px;
    background: #F9F5EF;
    padding: 117px 161px 76px;
    margin-left: -64px;
    z-index: -1;

    @include ml {
      padding: 70px 80px 70px 140px;
    }

    @include lg {
      margin-left: 0;
      padding: 50px;
      border-radius: 30px;
    }

    @include vxs {
      padding: 40px 15px;
    }

    .title-box {
      max-width: 595px;
    }

    .image {
      position: absolute;
      bottom: 0;
      right: 0;

      @media (max-width: 1800px) {
        display: none;
      }
    }
  }
}


.wave {
  position: absolute;
  top: 103px;
  left: 73px;
}

.letter {
  position: absolute;
  bottom: 26px;
  left: 94px;
}

.simple-block {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 38px;
    width: 4px;
    height: 170px;
    background-color: rgba(219, 234, 247, 1);
  }

  // .feature-box-two
  .feature-box-two {
    display: flex;
    margin-bottom: 35px;

    .feature-icon {
      position: relative;
      min-width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
      margin-right: 20px;
      border-radius: 50%;
      display: inline-block;
      transition: all ease 0.4s;
      z-index: 99;

      img {
        transition: all ease 0.4s;
        filter: none;
      }
    }

    &:last-child {
      .feature-info {
        margin-bottom: 0;
      }
    }

    .feature-title {
      margin-top: -0.25rem;
      margin-bottom: 7px;
    }

    .feature-text {
      margin-bottom: 0;
    }
  }
}

.mockup1 {
  padding-bottom: 310px;
}

@include lg {
  .img-box-2 {
    width: 100%;
    height: 450px;

    .small-img {
      @include equal-size(200px);
    }
  }
}

@include md {
  .img-box-2 {
    @include equal-size(480px);
    margin-left: auto;
    margin-right: auto;
  }

  .mockup1 {
    padding-bottom: 220px;
  }
}

@include xs {
  .img-box-2 {
    width: 100%;
    height: calc(100vw - 35px);
  }

  .feature-box {
    flex-direction: column;
    align-items: flex-start;

    .feature-icon {
      margin-bottom: 20px;
    }
  }
}

@include vxs {
  .img-box-2 {
    .big-img {
      top: 0;
      left: 0;
      max-width: calc(100% - 10px);
    }

    .small-img {
      display: none;
    }
  }
}








// ==================
// why-choose-section
// ==================

.why-section-two {
  position: relative;

  @include xs {
    #choose-tabContent {
      margin-top: 10px;
    }
  }

  @include md {
    margin-top: 0;
  }

  .glob {
    position: absolute;
    right: 91px;
    top: 129px;

    @include ml {
      display: none !important;
    }
  }

  .star {
    position: absolute;
    bottom: 58px;
    left: 199px;

    @include ml {
      display: none !important;
    }
  }

  .text-center {
    @include md {
      text-align: left !important;
    }
  }

  .nav-tabs {
    @include xs {
      border: 0;
    }
  }

  .choose-tab {
    margin-bottom: 53px;

    @include sm {
      margin-bottom: 0;
    }


    .choose-btn {
      padding: 12px 50px;

      @include ml {
        padding: 12px 44px;
      }

      @include sm {
        padding: 12px 39px;
      }

      @include xs {    
        margin-bottom: 15px;
        width: max-content;
        padding: 8px 19px;

        &.active {
          background-color: $theme-color;
        }
      }
    }
  }

  .content-box {
    @include lg {
      max-width: 673px;
    }

    .title {
      font-size: 30px;
      color: var(--title-color);
      font-weight: 700;
      line-height: 40px;
      margin-bottom: 12px;

      @include vxs {
        font-size: 26px;
        line-height: 36px;
      }
    }

    p {
      margin-bottom: 27px;
    }

    .check-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;

      @include sm {
        flex-direction: column;
        align-items: baseline;
      }

      ul {
        li {
          font-size: 18px;
          color: var(--body-color);
          padding-left: 27px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          &::before {
            width: 18px;
            height: 18px;
            line-height: 18px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
          }
        }
      }
    }
  }

  .content-box-two {
    .sec-title {
      @include xs {
        font-size: 40px;
      }
    }
  }

  .why-images {
    padding-right: 30px;

    @include lg {
      padding-right: 0;
      margin-bottom: 50px;
    }

    img {
      width: 100%;
      border-radius: 20px;
    }
  }
}

.ml-auto {
  margin-left: auto;

  @include md {
    margin: 30px auto 0;
  }
}


@include lg {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }
}