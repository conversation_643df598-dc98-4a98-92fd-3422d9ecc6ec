.process-card {
    border-radius: 40px;
    padding: 40px 40px 32px 40px;
    position: relative;
    background-color: $white-color;
    transition: all ease 0.4s;

    .process-number {
        @include equal-size-lineHeight(60px);
        background-color: #dbeaf7;
        color: $theme-color;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        border-radius: 50px;
        position: absolute;
        top: 35px;
        right: -30px;
    }

    .process-direction {
        position: absolute;
        left: calc(100% + 30px);
        top: 42px;
    }

    .process-head {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .process-icon {
        width: 50px;
        margin-right: 20px;

        img {
            transition: all ease 0.4s;
            filter: none;
        }
    }

    .process-title {
        max-width: 130px;
        margin-bottom: 0;
        transition: all ease 0.4s;
    }

    .process-text {
        margin-bottom: 0;
        transition: all ease 0.4s;
    }

    &:hover {
        background-color: $secondary-color;

        .process-icon {
            img {
                filter: brightness(0) invert(1);
            }
        }

        .process-title,
        .process-text {
            color: $white-color;
        }
    }
}

.process-area {
    position: relative;

    .process-line {
        width: calc(100% + 80px);
        position: absolute;
        top: 44%;
        left: -30px;
        z-index: -1;
    }
}

.process-box-body {
    width: 200px;
    height: 210px;
    box-shadow: 0px 20px 60px 0px rgba(16, 55, 65, 0.06);
    background-color: $white-color;
    text-align: center;
    margin-left: 24px;
    border-radius: 0 50% 50% 50%;
    position: relative;

    &:before {
        content: "";
        @include equal-size(calc(100% + 8px));
        border: 2px solid rgb(242, 244, 249);
        position: absolute;
        top: 2px;
        left: 2px;
        border-radius: 0 50% 50% 50%;
        z-index: -1;
        transition: all ease 0.4s;
    }

    .process-number {
        @include equal-size-lineHeight(60px);
        border-radius: 50%;
        color: $white-color;
        background-color: $theme-color;
        display: block;
        font-size: 24px;
        font-weight: 500;
        text-align: center;
        transform: translate(-24px, -24px);
        position: relative;

        &:before,
        &:after {
            content: "";
            @include equal-size(calc(100% + 40px));
            background-color: $theme-color;
            position: absolute;
            top: -20px;
            left: -20px;
            border-radius: 50%;
            box-shadow: 0px 20px 60px 0px rgba(0, 35, 160, 0.06);
            opacity: 0.6;
            z-index: -1;
            animation: numPulse 4s infinite;
        }

        &:after {
            animation-delay: 2s;
        }
    }

    .process-content {
        padding: 0 23px;
    }

    .process-icon {
        margin-bottom: 12px;
        display: block;
    }

    .process-name {
        font-weight: 600;
        color: $title-color;

        a {
            color: inherit;

            &:hover {
                color: $theme-color;
            }
        }
    }

    &:hover {
        &:before {
            border-color: $theme-color;
        }
    }
}

.process-box {
    &:nth-child(even) {
        .process-box-body {
            margin-top: 40px;
        }
    }
}

@keyframes numPulse {
    0% {
        transform: scale(0);
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@include lg {
    .process-card {
        .process-number {
            right: -10px;
        }

        .process-direction {
            display: none;
        }
    }

    .process-box-body {
        width: 187px;

        .process-content {
            padding: 0 18px;
        }
    }
}

@include md {
    .process-card .process-number {
        &:after {
            display: none;
        }
    }

    .process-box-body {
        width: 270px;

        .process-content {
            padding: 0 25px;
        }

        &.mt-40 {
            margin-top: 0;
        }
    }

    .process-area .process-line {
        display: none;
    }
}

@include sm {
    .process-title {
        max-width: 116px;
    }

    .process-box-body {
        width: 200px;

        .process-content {
            padding: 0 30px;
        }
    }
}

@include xs {
    .process-card {
        border-radius: 30px;

        .process-number {
            @include equal-size-lineHeight(45px);
            background-color: #dbeaf7;
            font-size: 20px;
            top: 15px;
            right: -5px;
        }
    }

    .process-box-body {
        max-width: 280px;
    }

    .process-area {
        .row.justify-content-between {
            justify-content: center !important;
        }
    }
}