.product-thumb img {
    cursor: pointer;
}

.product-big-img {
    --gap: 50px;
    position: relative;

    img {
        border-radius: 20px;
    }

    .add_to_wishlist {
        position: absolute;
        top: calc(var(--gap) - 5px);
        right: var(--gap);
    }

    .product-thumb-area {
        width: calc(100% - var(--gap) * 2);
        position: absolute;
        bottom: var(--gap);
        left: var(--gap);
    }
}

.product-about {
    margin-top: -1.5rem;

    .woocommerce-product-rating {
        display: inline-block;
        margin-bottom: 8px;
    }

    .star-rating {
        display: inline-block;
        vertical-align: middle;
    }

    .woocommerce-review-link {
        color: $body-color;

        &:hover {
            color: $theme-color;
        }
    }

    .price {
        font-size: 30px;
    }

    .actions {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 30px;
        margin-bottom: 25px;

        .quantity {
            margin-right: 25px;
        }
    }
}

.product_meta {
    font-weight: 500;
    font-size: 18px;
    text-transform: capitalize;
    font-family: $para-font;

    >span {
        display: block;
        margin-bottom: 5px;
        color: $title-color;

        &:last-child {
            margin-bottom: 0;
        }

        a {
            color: inherit;

            &:hover {
                color: $theme-color;
            }
        }

        >a,
        >span {
            position: relative;
            color: $body-color;

            &:after {
                content: ",";
                margin-right: 5px;
            }

            &:last-child {
                &:after {
                    display: none;
                }
            }

            &:first-child {
                margin-left: 7px;
            }
        }
    }
}

.actions.d-flex {
    flex-wrap: wrap;
    gap: 12px;
}

.product-tab-style1 {
    border-bottom: 1px solid #ececec;
    position: relative;

    a {
        color: $title-color;
        font-size: 20px;
        font-weight: 600;
        text-transform: capitalize;
        display: inline-block;
        position: relative;
        line-height: 1;
        padding: 0 0 13px 0;
        margin-right: 45px;
        font-family: $title-font;
        position: relative;

        &.active {
            color: $theme-color;
        }
    }

    .indicator {
        position: absolute;
        left: 0;
        top: auto !important;
        height: 0 !important;
        bottom: -1px;
        -webkit-transition: all ease 0.4s;
        transition: all ease 0.4s;
        border-bottom: 2px solid $theme-color;
    }
}

.product-desc-area {
    p {
        margin-bottom: 25px;
    }

    ul,
    .check-list {
        margin-bottom: 25px;
    }
}

#description>p:first-child {
    margin-top: -7px;
}

@include lg {
    .product-about .price {
        font-size: 26px;
    }
}

@include md {
    .product-about {
        margin-top: 0;

        .price {
            font-size: 24px;
        }
    }

    .product-tab-style1 a {
        font-size: 18px;
        margin-right: 30px;
    }
}

@include sm {
    .product-about {
        .product-title {
            margin-bottom: 7px;
        }

        .price {
            font-size: 20px;
        }
    }

    .product-about .actions .quantity {
        margin-right: 0;
    }
}

@include xs {
    .product-tab-style1 {
        .indicator {
            display: none;
        }
    }
}

@include vxs {
    .product-big-img {
        --gap: 20px;
    }

    .quantity {
        width: 110px;

        input {
            width: 36px;
            height: 36px;
        }

        .qut-btn {
            width: 36px;
        }
    }

    .actions.d-flex {
        gap: 12px;
    }

}