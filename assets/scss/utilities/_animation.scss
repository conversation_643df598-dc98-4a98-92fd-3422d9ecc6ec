.ripple-animation {
  animation-duration: var(--ripple-ani-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-name: ripple;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0;
  }

  30% {
    opacity: 0.4;
  }

  100% {
    transform: scale(1.7);
    opacity: 0;
  }
}

.scalein.vs-animated {
  --animation-name: scalein;
}

.slidetopleft.vs-animated {
  --animation-name: slidetopleft;
}

.slidebottomright.vs-animated {
  --animation-name: slidebottomright;
}

.slideinleft.vs-animated {
  --animation-name: slideinleft;
}

.slideinright.vs-animated {
  --animation-name: slideinright;
}

.slideinup.vs-animated {
  --animation-name: slideinup;
}

.slideindown.vs-animated {
  --animation-name: slideindown;
}

.rollinleft.vs-animated {
  --animation-name: rollinleft;
}

.rollinright.vs-animated {
  --animation-name: rollinright;
}


.scalein,
.slidetopleft,
.slidebottomright,
.slideinleft,
.slideinright,
.slideindown,
.slideinup,
.rollinleft,
.rollinright {
  opacity: 0;
  animation-fill-mode: both;
  animation-iteration-count: 1;
  animation-duration: 1s;
  animation-delay: 0.3s;
  animation-name: var(--animation-name);
}

.vs-animated {
  opacity: 1;
}

@keyframes slideinup {
  0% {
    opacity: 0;
    transform: translateY(70px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes slideinright {
  0% {
    opacity: 0;
    transform: translateX(70px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slideindown {
  0% {
    opacity: 0;
    transform: translateY(-70px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes slideinleft {
  0% {
    opacity: 0;
    transform: translateX(-70px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slidebottomright {
  0% {
    opacity: 0;
    transform: translateX(100px) translateY(100px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes slidetopleft {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes scalein {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  100% {
    transform: scale(1);

  }
}

@keyframes rollinleft {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
  }

  to {
    transform: translateX(0) rotate(0deg);
  }
}

@keyframes rollinright {
  0% {
    opacity: 0;
    transform: translateX(100%) rotate(120deg);
  }

  to {
    transform: translateX(0) rotate(0deg);
  }
}



.shapePulse {
  animation: shapePluse 6s linear infinite;
}

@keyframes shapePluse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.rotate {
  animation: rotateShape 10s linear infinite;
}

@keyframes rotateShape {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.moving {
  animation: moving 8s linear infinite;
}

@keyframes moving {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-50px);
  }

  100% {
    transform: translateY(0);
  }
}

.movingX {
  animation: movingX 8s linear infinite;
}

@keyframes movingX {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(-50px);
  }

  100% {
    transform: translateX(0);
  }
}

.movingX-reverse {
  animation: movingXreverse 8s linear infinite;
}

@keyframes movingXreverse {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(50px);
  }

  100% {
    transform: translateX(0);
  }
}

.movingTopLeft {
  animation: movingTopLeft 10s linear infinite;
}

@keyframes movingTopLeft {
  0% {
    opacity: 1;
    transform: translate(0, 0);
  }

  100% {
    opacity: 0;
    transform: translate(-200px, -200px);
  }
}

.movingBottomLeft {
  animation: movingBottomLeft 10s linear infinite;
}

@keyframes movingBottomLeft {
  0% {
    opacity: 1;
    transform: translate(0, 0);
  }

  100% {
    opacity: 0;
    transform: translate(150px, 150px);
  }
}

// Animate.css 
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}