@charset "UTF-8";

/*
Template Name: <PERSON><PERSON><PERSON>
Template URL: https://themeforest.vecuro.com/knirpse/
Description: K<PERSON>pse - Kindergarten & Baby Care Template
Author: vecuro_themes
Author URI: https://themeforest.net/user/vecuro_themes
Version: 1.0.0
*/
/*=================================
    CSS Index Here
==================================*/
/*

01. Theme Base
    1.1. Mixin
    1.2. Function
    1.3. Variable
    1.4. Typography
    1.5. Extend
    1.7. Wordpress Default
02. Reset
    2.1. Container
    2.2. Grid
    2.3. Input
    2.4. Slick Slider
    2.5. Mobile Menu
03. Utilities
    3.1. Preloader
    3.2. Buttons
    3.3. Titles
    3.4. Common
    3.6. Font
    3.7. Background
    3.8. Text Color
    3.9. Overlay
    3.10. Animation
04. Template Style
    4.1. Widget
    4.2. Header
    4.3. Footer
    4.4. Breadcumb
    4.5. Pagination
    4.6. Blog
    4.7. Comments
    4.8. Hero Area
    4.9. Error    
    4.10. Popup Search
    4.11. Popup Side Menu
    4.12. Wocommerce
    4.13. Products
    4.14. Cart
    4.15. Checkout
    4.16. Wishlist
    4.18. simple section
    4.19. Service
    4.20. About
    4.21. Counter
    4.22. Class
    4.23. Process
    4.24. Appointment
    4.25. Team
    4.26. Project Gallery
    4.27. Testimonial
    4.28. Choose
    4.29. Event
    4.30. Contact
05. Spacing

*/
/*=================================
    CSS Index End
==================================*/
/*=================================
   01. Theme Base
==================================*/
/*------------------- 1.1. Mixin -------------------*/
/*------------------- 1.2. Function -------------------*/
/*------------------- 1.3. Variable-------------------*/
:root {
  --theme-color: #136693;
  /* --theme-color: #312783; */
  /* --secondary-color: #88c90d; */
  --secondary-color: #f19433;
  --title-color: #103741;
  --body-color: #74787c;
  --smoke-color: #f9f6ef;
  --smoke-theme: #fef5ff;
  --light-color: #9b9b9b;
  --black-color: #000000;
  --white-color: #ffffff;
  --yellow-color: #fec624;
  --success-color: #28a745;
  --error-color: #dc3545;
  --blue-color: #786acf;
  --border-color: rgb(238, 241, 242);
  --title-font: "Baloo 2", cursive;
  --para-font: "Catamaran", sans-serif;
  --body-font: "Baloo 2", cursive;
  --icon-font: "Font Awesome 6 Pro";
  --main-container: 1220px;
  --container-gutters: 30px;
  --section-space: 120px;
  --section-space-mobile: 80px;
  --section-title-space: 60px;
  --ripple-ani-duration: 5s;
}

/*------------------- 1.5. Typography -------------------*/
html,
body {
  scroll-behavior: auto !important;
  overflow-x: hidden;
}

body {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  line-height: 26px;
  -webkit-font-smoothing: antialiased;
}

iframe {
  border: none;
  width: 100%;
}

.slick-slide:focus,
button:focus,
a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

input:focus {
  outline: none;
  box-shadow: none;
}

img:not([draggable]),
embed,
object,
video {
  max-width: 100%;
  height: auto;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

ol li,
ul li {
  font-family: var(--para-font);
}

ol li a,
ul li a {
  font-family: inherit;
}

table {
  margin: 0 0 1.5em;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid var(--border-color);
}

th {
  font-weight: 700;
  /* color: var(--title-color); */
  color: white;
}

td,
th {
  border: 2px solid #aab9be;
  padding: 9px 12px;
}

a {
  color: var(--theme-color);
  text-decoration: none;
  outline: 0;
  transition: all ease 0.4s;
}

a:hover {
  color: var(--title-color);
}

a:active,
a:focus,
a:hover,
a:visited {
  text-decoration: none;
  outline: 0;
}

button {
  transition: all ease 0.4s;
}

img {
  border: none;
  max-width: 100%;
}

ins {
  text-decoration: none;
}

pre {
  font-family: var(--para-font);
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

span.ajax-loader:empty,
p:empty {
  display: none;
}

p {
  font-family: var(--para-font);
  margin: 0 0 18px 0;
  color: #303030;
  line-height: 1.625;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: none;
  font-weight: bold;
  line-height: 1.208;
  margin: 0 0 15px 0;
}

.h1,
h1 {
  font-size: 48px;
}

.h2,
h2 {
  font-size: 36px;
}

.h3,
h3 {
  font-size: 30px;
}

.h4,
h4 {
  font-size: 24px;
}

.h5,
h5 {
  font-size: 20px;
}

.h6,
h6 {
  font-size: 16px;
}

/* Large devices */
@media (max-width: 1199px) {

  .h1,
  h1 {
    font-size: 40px;
  }

  .h2,
  h2 {
    font-size: 30px;
  }

  .h3,
  h3 {
    font-size: 24px;
  }

  .h4,
  h4 {
    font-size: 22px;
  }
}

/* Small devices */
@media (max-width: 767px) {

  .h1,
  h1 {
    font-size: 30px;
  }

  .h2,
  h2 {
    font-size: 26px;
  }

  .h3,
  h3 {
    font-size: 22px;
  }

  .h4,
  h4 {
    font-size: 22px;
  }

  .h5,
  h5 {
    font-size: 18px;
  }
}

/*------------------- 1.6. Extend -------------------*/
/*------------------- 1.7. Wordpress Default -------------------*/
/*=================================
    02. Reset
==================================*/
/*------------------- 2.1. Container -------------------*/
@media only screen and (min-width: 1300px) {

  .container-xxl,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: calc(var(--main-container) + var(--container-gutters));
    padding-left: calc(var(--container-gutters) / 2);
    padding-right: calc(var(--container-gutters) / 2);
  }
}

@media only screen and (max-width: 1600px) {
  .container-fluid.px-0 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  .container-fluid.px-0 .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/*------------------- 2.2. Grid -------------------*/
.slick-track>[class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) / 2);
  padding-left: calc(var(--bs-gutter-x) / 2);
  margin-top: var(--bs-gutter-y);
}

@media (min-width: 1199px) {
  .row {
    --bs-gutter-x: 30px;
  }
}

.gy-30 {
  --bs-gutter-y: 30px;
}

.g-0 {
  --bs-gutter-x: 0px;
  --bs-gutter-y: 0px;
}

.gx-15 {
  --bs-gutter-x: 15px;
}

.gx-20,
.g-20 {
  --bs-gutter-x: 20px;
}

.gy-20,
.g-20 {
  --bs-gutter-y: 20px;
}

.gx-40 {
  --bs-gutter-x: 40px;
}

.gy-40 {
  --bs-gutter-y: 40px;
}

.gx-60 {
  --bs-gutter-x: 60px;
}

.gy-60 {
  --bs-gutter-y: 60px;
}

.gx-70 {
  --bs-gutter-x: 70px;
}

.gy-70 {
  --bs-gutter-y: 70px;
}

.gx-150 {
  --bs-gutter-x: 150px;
}

@media (min-width: 1399px) {
  .gx-30 {
    --bs-gutter-x: 30px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .gx-150 {
    --bs-gutter-x: 30px;
  }

  .gx-60 {
    --bs-gutter-x: 40px;
  }

  .gx-70 {
    --bs-gutter-x: 30px;
  }

  .gx-40 {
    --bs-gutter-x: 30px;
  }

  .gy-40 {
    --bs-gutter-y: 30px;
  }

  .gx-lg-15 {
    --bs-gutter-x: 15px;
  }
}

/*------------------- 2.3. Input -------------------*/
select {
  display: block;
  width: 100%;
  line-height: 1.5;
  vertical-align: middle;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select,
.form-select,
.form-control {
  height: 50px;
  padding: 0 30px;
  padding-right: 60px;
  border: 1px solid var(--border-color);
  color: var(--body-color);
  background-color: var(--white-color);
  border-radius: 5px;
  font-weight: 400;
  font-size: 16px;
}

select:focus,
.form-select:focus,
.form-control:focus {
  outline: 0;
  box-shadow: none;
  background-color: var(--white-color);
  border-color: #e3e3e3;
}

select::-moz-placeholder,
.form-select::-moz-placeholder,
.form-control::-moz-placeholder {
  color: var(--body-color);
}

select::-webkit-input-placeholder,
.form-select::-webkit-input-placeholder,
.form-control::-webkit-input-placeholder {
  color: var(--body-color);
}

select:-ms-input-placeholder,
.form-select:-ms-input-placeholder,
.form-control:-ms-input-placeholder {
  color: var(--body-color);
}

select::placeholder,
.form-select::placeholder,
.form-control::placeholder {
  color: var(--body-color);
}

.form-style2 select,
.form-style2 .form-select,
.form-style2 .form-control {
  border: none;
}

.form-select {
  cursor: pointer;
  background-position: right 1.3rem center;
}

input.form-control::-webkit-outer-spin-button,
input.form-control::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input.form-control[type=number] {
  -moz-appearance: textfield;
}

textarea.form-control {
  min-height: 150px;
  padding-top: 17px;
  padding-bottom: 17px;
}

input[type=checkbox] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}

input[type=checkbox]:checked~label:before {
  content: "\f00c";
  color: var(--theme-color);
  border-color: var(--theme-color);
}

input[type=checkbox]~label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  display: block;
}

input[type=checkbox]~label:before {
  content: "";
  font-family: var(--icon-font);
  font-weight: 700;
  position: absolute;
  left: 0;
  top: 4px;
  background-color: var(--white-color);
  border: 1px solid var(--border-color);
  height: 18px;
  width: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
}

input[type=radio] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}

input[type=radio]~label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  line-height: 1;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0;
}

input[type=radio]~label::before {
  content: "\f111";
  position: absolute;
  font-family: var(--icon-font);
  left: 0;
  top: 0;
  width: 15px;
  height: 15px;
  padding-left: 0.5px;
  font-size: 0.29rem;
  line-height: 14.9px;
  text-align: center;
  border: 1px solid var(--theme-color);
  border-radius: 100%;
  font-weight: 700;
  background: var(--white-color);
  color: transparent;
  transition: all 0.2s ease;
}

input[type=radio]:checked~label::before {
  border-color: var(--theme-color);
  background-color: var(--theme-color);
  color: var(--white-color);
}

label {
  margin-bottom: 0.6em;
  margin-top: -0.3em;
  display: block;
}

.form-group {
  margin-bottom: var(--bs-gutter-x);
  position: relative;
}

.form-group>i {
  position: absolute;
  right: 30px;
  top: 20px;
  font-size: 15px;
  color: var(--theme-color);
}

select.style2~i,
.form-select.style2~i,
.form-control.style2~i {
  left: 40px;
  top: 14px;
  color: var(--body-color);
  width: 25px;
}

select.style2,
.form-select.style2,
.form-control.style2 {
  padding-right: 30px;
  padding-left: 56px;
  background-color: var(--smoke-color);
  border: none;
  border-radius: 30px;
  resize: none;
}

select.style2:focus~i,
.form-select.style2:focus~i,
.form-control.style2:focus~i {
  color: var(--title-color);
}

select.style2.form-select,
.form-select.style2.form-select,
.form-control.style2.form-select {
  padding-left: 30px;
  background-position: right 1.8rem center;
}

textarea.form-control.style2~i {
  top: 16px;
}

.form-control.is-invalid,
.was-validated .form-control:invalid {
  border: 1px solid var(--error-color) !important;
  background-position: right calc(0.375em + 0.8875rem) center;
  background-image: none;
}

.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  outline: 0;
  box-shadow: none;
}

textarea.form-control.is-invalid {
  background-position: top calc(0.375em + 0.5875rem) right calc(0.375em + 0.8875rem);
}

.row.no-gutters>.form-group {
  margin-bottom: 0;
}

.form-messages {
  display: none;
}

.form-messages.mb-0 * {
  margin-bottom: 0;
}

.form-messages.success {
  color: var(--success-color);
  display: block;
}

.form-messages.error {
  color: var(--error-color);
  display: block;
}

/* Extra small devices */
@media (max-width: 575px) {

  .form-select,
  .form-control {
    padding-right: 30px;
    padding-left: 15px;
  }
}

/*------------------- 2.4. Slick Slider -------------------*/
.row>.slick-list {
  padding-left: 0;
  padding-right: 0;
}

.slick-track {
  min-width: 100%;
}

.slick-slide img {
  display: inline-block;
}

.slick-dots {
  list-style-type: none;
  padding: 2px 0;
  margin: 30px 0 30px 0;
  line-height: 0;
  text-align: center;
  height: max-content;
}

.slick-dots li {
  display: inline-block;
  margin-right: 10px;
}

.slick-dots li:last-child {
  margin-right: 0;
}

.slick-dots button {
  font-size: 0;
  padding: 0;
  background-color: transparent;
  width: 12px;
  height: 12px;
  line-height: 0;
  border-radius: 9999px;
  border: 3px solid #7a7a7a;
  transition: all ease 0.4s;
}

.slick-dots button:hover {
  border-color: var(--theme-color);
}

.slick-dots .slick-active button {
  transform: scale(1.5);
  border-color: var(--theme-color);
}

.slick-arrow {
  display: inline-block;
  padding: 0;
  background-color: transparent;
  color: var(--theme-color);
  position: absolute;
  top: 50%;
  margin-right: 50px;
  margin-top: calc(var(--icon-size, 60px) / -2);
  border: 1px solid var(--theme-color);
  left: var(--pos-x, -170px);
  width: var(--icon-size, 60px);
  height: var(--icon-size, 60px);
  line-height: 62px;
  border-radius: 50%;
  z-index: 2;
  font-size: 18px;
}

.slick-arrow.default {
  position: relative;
  --pos-x: 0;
  margin-top: 0;
}

.slick-arrow:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.slick-arrow:hover .long-arrow {
  background-color: var(--theme-color);
  animation: arrow-left ease 1s alternate;
}

.slick-arrow:hover .long-arrow:before {
  border-right-color: var(--theme-color);
}

.slick-arrow.slick-next {
  margin-right: 0;
  margin-left: 50px;
  right: var(--pos-x, -170px);
  left: auto;
}

.slick-arrow.slick-next .long-arrow {
  left: auto;
  right: 50%;
  margin-left: 0;
  margin-right: -3px;
}

.slick-arrow.slick-next .long-arrow:before {
  left: auto;
  right: -1px;
  border-right: none;
  border-left: 7px solid var(--title-color);
}

.slick-arrow.slick-next:hover .long-arrow {
  animation: arrow-right ease 1s alternate;
}

.slick-arrow.slick-next:hover .long-arrow:before {
  border-right-color: transparent;
  border-left-color: var(--theme-color);
}

.arrow-wrap .slick-arrow {
  opacity: 0;
  visibility: hidden;
  left: var(--pos-x, -220px);
}

.arrow-wrap .slick-arrow.slick-next {
  right: var(--pos-x, -220px);
  left: auto;
}

.arrow-wrap:hover .slick-arrow {
  opacity: 1;
  visibility: visible;
  left: var(--pos-x, -170px);
}

.arrow-wrap:hover .slick-arrow.slick-next {
  right: var(--pos-x, -170px);
  left: auto;
}

.bg-smoke .slick-arrow {
  background-color: var(--white-color);
  border-color: transparent;
}

.bg-smoke .slick-arrow:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

/* Extra large devices */
@media (max-width: 1500px) {
  .slick-arrow {
    --arrow-horizontal: -20px;
  }

  .arrow-wrap .slick-arrow {
    left: var(--pos-x, -120px);
  }

  .arrow-wrap .slick-arrow.slick-next {
    right: var(--pos-x, -120px);
    left: auto;
  }

  .arrow-wrap:hover .slick-arrow {
    left: var(--pos-x, -60px);
  }

  .arrow-wrap:hover .slick-arrow.slick-next {
    right: var(--pos-x, -60px);
    left: auto;
  }
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .slick-arrow {
    --arrow-horizontal: 40px;
  }

  .arrow-wrap:hover .slick-arrow {
    left: var(--pos-x, -15px);
  }

  .arrow-wrap:hover .slick-arrow.slick-next {
    right: var(--pos-x, -15px);
    left: auto;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .slick-arrow {
    --icon-size: 40px;
    margin-right: 40px;
  }

  .slick-arrow .long-arrow {
    width: 62px;
    height: 2px;
  }

  .slick-arrow.slick-next {
    margin-right: 0;
    margin-left: 40px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .slick-arrow {
    --arrow-horizontal: 40px;
  }

  .arrow-wrap:hover .slick-arrow {
    left: var(--pos-x, 0);
  }

  .arrow-wrap:hover .slick-arrow.slick-next {
    right: var(--pos-x, 0);
    left: auto;
  }
}

/*------------------- 2.5. Mobile Menu -------------------*/
.vs-menu-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999999;
  width: 0;
  width: 100%;
  height: 100%;
  transition: all ease 0.8s;
  opacity: 0;
  visibility: hidden;
}

.vs-menu-wrapper .mobile-logo {
  padding-bottom: 30px;
  padding-top: 40px;
  display: block;
  text-align: center;
  background-color: rgba(28, 185, 200, 0.1);
}

.vs-menu-wrapper .mobile-logo svg {
  max-width: 185px;
}

.vs-menu-wrapper .vs-menu-toggle {
  border: none;
  font-size: 22px;
  position: absolute;
  right: 10px;
  top: 10px;
  padding: 0;
  line-height: 1;
  width: 33px;
  height: 33px;
  line-height: 33px;
  font-size: 18px;
  z-index: 1;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
}

.vs-menu-wrapper .vs-menu-toggle:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

.vs-menu-wrapper .vs-menu-area {
  width: 100%;
  max-width: 310px;
  background-color: #fff;
  border-right: 3px solid var(--theme-color);
  height: 100%;
  position: relative;
  left: -110%;
  opacity: 0;
  visibility: hidden;
  transition: all ease 1s;
  z-index: 1;
}

.vs-menu-wrapper.vs-body-visible {
  opacity: 1;
  visibility: visible;
}

.vs-menu-wrapper.vs-body-visible .vs-menu-area {
  left: 0;
  opacity: 1;
  visibility: visible;
}

.vs-mobile-menu {
  overflow-y: scroll;
  max-height: calc(100vh - 200px);
  padding-bottom: 40px;
  margin-top: 33px;
  text-align: left;
}

.vs-mobile-menu ul {
  margin: 0;
  padding: 0 0;
}

.vs-mobile-menu ul li {
  border-bottom: 1px solid #fdedf1;
  list-style-type: none;
}

.vs-mobile-menu ul li li:first-child {
  border-top: 1px solid #fdedf1;
}

.vs-mobile-menu ul li a {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--title-color);
}

.vs-mobile-menu ul li a:before {
  content: "\f105";
  font-family: var(--icon-font);
  position: relative;
  left: 0;
  top: 0;
  margin-right: 10px;
  display: inline-block;
}

.vs-mobile-menu ul li.vs-active>a {
  color: var(--theme-color);
}

.vs-mobile-menu ul li.vs-active>a:before {
  transform: rotate(90deg);
}

.vs-mobile-menu ul li ul li {
  padding-left: 20px;
}

.vs-mobile-menu ul li ul li:last-child {
  border-bottom: none;
}

.vs-mobile-menu ul .vs-item-has-children>a .vs-mean-expand {
  position: absolute;
  right: 0;
  top: 50%;
  font-weight: 400;
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  margin-top: -12.5px;
  display: inline-block;
  text-align: center;
  background-color: var(--smoke-color);
  color: var(--title-color);
  box-shadow: 0 0 20px -8px rgba(173, 136, 88, 0.5);
  border-radius: 50%;
}

.vs-mobile-menu ul .vs-item-has-children>a .vs-mean-expand:before {
  content: "\f067";
  font-family: var(--icon-font);
}

.vs-mobile-menu ul .vs-item-has-children>a:after {
  content: "\f067";
  font-family: var(--icon-font);
  width: 18px;
  height: 18px;
  line-height: 18px;
  display: inline-block;
  text-align: center;
  font-size: 12px;
  border-radius: 50px;
  background-color: var(--smoke-color);
  float: right;
}

.vs-mobile-menu ul .vs-item-has-children.vs-active>a .vs-mean-expand:before {
  content: "\f068";
}

.vs-mobile-menu ul .vs-item-has-children.vs-active>a:after {
  content: "\f068";
}

.vs-mobile-menu>ul {
  padding: 0 40px;
}

.vs-mobile-menu>ul>li:last-child {
  border-bottom: none;
}

.vs-menu-toggle {
  width: 50px;
  height: 50px;
  padding: 0;
  font-size: 20px;
  border: none;
  background-color: var(--theme-color);
  color: var(--white-color);
  display: inline-block;
  border-radius: 50%;
}

.vs-menu-toggle:hover {
  background-color: var(--title-color);
}

.vs-menu-toggle.style-text,
.vs-menu-toggle.style-text-white {
  width: auto;
  height: auto;
  background-color: transparent;
  color: var(--title-color);
  font-size: 20px;
}

.vs-menu-toggle.style-text i,
.vs-menu-toggle.style-text-white i {
  margin-right: 10px;
}

.vs-menu-toggle.style-text-white {
  color: var(--white-color);
}

@media (max-width: 400px) {
  .vs-menu-wrapper .vs-menu-area {
    width: 100%;
    max-width: 270px;
  }

  .vs-mobile-menu>ul {
    padding: 0 20px;
  }
}

/*=================================
    03. Utilities
==================================*/
/*------------------- 3.1. Preloader -------------------*/
.preloader {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
  background-color: var(--white-color);
}

.preloader .vs-btn {
  border-radius: 0;
  padding: 7px 22px;
}

.preloader-inner {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.preloader-inner img {
  display: block;
  margin: 0 auto 10px auto;
}

.preloader-inner svg {
  animation: preloadAni 2s linear infinite;
  height: 80px;
  width: 80px;
}

@keyframes preloadAni {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

/*------------------- 3.2. Buttons -------------------*/
.vs-btn {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 18px;
  font-weight: 500;
  padding: 11px 33px;
  line-height: 1.6;
  text-transform: capitalize;
  min-width: 150px;
  text-align: center;
  border-radius: 9999px;
  border: none;
  display: inline-block;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.vs-btn i {
  font-size: 16px;
  margin-right: 5px;
}

.vs-btn:hover {
  color: var(--white-color);
  background-color: var(--secondary-color);
}

.vs-btn.btn-white {
  background-color: var(--white-color);
  color: var(--theme-color);
}

.vs-btn.btn-white:hover {
  color: var(--white-color);
}

.vs-btn.style-1 {
  background-color: transparent;
  border: 1px solid var(--theme-color);
  color: var(--theme-color);
  padding: 10px 33px;
}

.vs-btn.style-1:hover {
  border-color: var(--secondary-color);
  color: var(--white-color);
}

.vs-btn.style-2 {
  width: 160px;
  background-color: rgba(255, 255, 255, 0.145);
}

.vs-btn.style-2:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

.vs-btn.btn-sm {
  padding: 9px 20px;
  min-width: 120px;
  height: 40px;
  font-size: 14px;
}

/* Button Hover */
.wave-btn {
  transition: 0.21s;
}

.btn-hover {
  z-index: -1;
  position: absolute;
  left: -1px;
  top: -1px;
  width: 110%;
  height: 110%;
  border-radius: 50px;
}

.btn-hover .btn-hover-inner {
  position: relative;
  display: block;
  height: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
}

.btn-hover .part {
  position: absolute;
  top: -5px;
  width: 25%;
  height: 100%;
  border-radius: 100%;
  transform: translate3d(0, 150%, 0) scale(1.7);
  transition: transform 0.21s !important;
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-hover .part:nth-child(1) {
  left: 0;
  transition-delay: 0s !important;
}

.btn-hover .part:nth-child(2) {
  left: 30%;
  transition-delay: 0.07s !important;
}

.btn-hover .part:nth-child(3) {
  left: 60%;
  transition-delay: 0.14s !important;
}

.btn-hover .part:nth-child(4) {
  left: 90%;
  transition-delay: 0.21s !important;
}

.wave-btn:hover,
.wave-btn:active,
.wave-btn:focus {
  transition-delay: 0.21s;
}

.wave-btn:hover .part,
.wave-btn:active .part,
.wave-btn:focus .part {
  transform: translateZ(0) scale(1.7) !important;
}

/* Extra small devices */
@media (max-width: 416px) {
  .vs-btn {
    font-size: 14px;
    padding: 8px 25px;
    min-width: 140px;
  }

  .vs-btn i {
    font-size: 14px;
  }

  .vs-btn.btn-60 {
    height: 60px;
  }

  .vs-btn.style-1 {
    padding: 7px 25px;
  }

  .vs-btn.style-2 {
    width: 150px;
  }
}

.icon-btn {
  display: inline-block;
  width: var(--btn-size, 50px);
  height: var(--btn-size, 50px);
  line-height: var(--btn-size, 52px);
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  border-radius: 50%;
  border: none;
}

.icon-btn:hover,
.icon-btn.active {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.icon-btn.style-2 {
  background-color: transparent;
  border: 1px solid var(--theme-color);
  color: var(--theme-color);
  line-height: 1;
}

.icon-btn.style-2:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.icon-btn.style-3 {
  background-color: var(--smoke-color);
  color: var(--theme-color);
  line-height: 1;
}

.icon-btn.style-3:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.icon-btn.style-4 {
  background-color: var(--white-color);
  color: var(--theme-color);
  line-height: 1;
}

.icon-btn.style-4:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.play-btn {
  position: relative;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  color: var(--white-color);
  border: 2px solid;
  width: 100px;
  height: 100px;
}

.play-btn>i {
  color: var(--white-color);
  position: relative;
  z-index: 2;
  line-height: 97px;
  text-align: center;
  font-size: 32px;
  border-radius: inherit;
  transition: all ease 0.4s;
}

.play-btn.style-1 {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
  /* Extra small devices */
}

.play-btn.style-1::before {
  content: "";
  position: absolute;
  border: 2px solid var(--theme-color);
  width: 150px;
  height: 150px;
  top: -25px;
  left: -25px;
  z-index: 1;
  border-radius: 50%;
  /* Extra small devices */
}

@media (max-width: 575px) {
  .play-btn.style-1::before {
    width: 70px;
    height: 70px;
    left: -12px;
    top: -12px;
  }
}

.play-btn.style-1::after {
  content: "";
  position: absolute;
  border: 2px solid var(--theme-color);
  width: 195px;
  height: 195px;
  top: -46px;
  left: -46px;
  z-index: 1;
  border-radius: 50%;
  /* Extra small devices */
}

@media (max-width: 575px) {
  .play-btn.style-1::after {
    width: 90px;
    height: 90px;
    left: -22px;
    top: -22px;
  }
}

.play-btn.style-1:hover {
  color: var(--theme-color);
  background-color: var(--white-color);
  border-color: var(--white-color);
}

.play-btn.style-1:hover i {
  color: inherit;
}

@media (max-width: 575px) {
  .play-btn.style-1 {
    width: 50px;
    height: 50px;
  }

  .play-btn.style-1 i {
    font-size: 14px;
    line-height: 47px;
  }
}

.link-btn {
  font-size: 18px;
  font-weight: 500;
  position: relative;
}

.link-btn i {
  margin-left: 2px;
  transition: 0.3s ease-in-out;
  line-height: 1;
  position: relative;
  top: 2px;
}

.link-btn:before {
  content: "";
  height: 1px;
  width: 0;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 3px;
  left: 0;
  transition: 0.3s ease-in-out;
}

.link-btn:hover,
.link-btn:hover i {
  color: var(--secondary-color);
}

.link-btn:hover:before {
  width: 100%;
}

.simple-icon {
  border: none;
  background-color: transparent;
  font-size: 20px;
  color: var(--title-color);
  margin: 0;
  padding: 0;
}

.simple-icon:hover,
.simple-icon.ative {
  color: var(--theme-color);
}

.scroll-btn {
  position: fixed;
  bottom: 300px;
  right: 30px;
  z-index: 94;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  border-radius: 50%;
  /* Small devices */
}

.scroll-btn i {
  display: inline-block;
  background-color: var(--theme-color);
  color: var(--white-color);
  text-align: center;
  font-size: 16px;
  width: var(--btn-size, 50px);
  height: var(--btn-size, 50px);
  line-height: var(--btn-size, 50px);
  z-index: 2;
  border-radius: inherit;
  position: relative;
  transition: all ease 0.8s;
}

.scroll-btn:focus i,
.scroll-btn:hover i {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.scroll-btn.show {
  bottom: 120px;
  opacity: 1;
  visibility: visible;
}

@media (max-width: 767px) {
  .scroll-btn {
    --btn-size: 40px;
    --extra-shape: -4px;
    right: 15px;
    bottom: 50px;
  }

  .scroll-btn.show {
    bottom: 15px;
  }
}

.scrollToTop {
  position: fixed;
  right: 30px;
  bottom: 500px;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
  z-index: 96;
}

.scrollToTop.show {
  bottom: 60px;
  opacity: 1;
  visibility: visible;
}

/* Small devices */
@media (max-width: 767px) {
  .play-btn {
    --icon-size: 60px;
  }

  .scrollToTop {
    right: 20px;
  }

  .scrollToTop.show {
    bottom: 20px;
  }
}

/*------------------- 3.3. Titles -------------------*/
.sec-title {
  font-size: 44px;
  margin-top: -0.8rem;
  line-height: 1.208;
}

.sec-title.big-title {
  font-size: 60px;
  margin-top: -1.1rem;
}

.sub-title {
  font-size: 24px;
  font-weight: 500;
  color: var(--theme-color);
  display: block;
  margin-bottom: 29px;
  margin-top: -4px;
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 15px);
}

/* Large devices */
@media (max-width: 1199px) {
  .sec-title {
    font-size: 42px;
  }

  .sec-title.big-title {
    font-size: 48px;
    margin-top: -0.8rem;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .title-area {
    margin-bottom: calc(var(--section-title-space) - 25px);
  }

  .sub-title {
    margin-bottom: 20px;
  }

  .sec-title {
    font-size: 36px;
  }

  .sec-title.big-title {
    font-size: 40px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .sec-title {
    font-size: 30px;
  }

  .sec-title.big-title {
    font-size: 36px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .sub-title {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .sec-title {
    font-size: 24px;
  }

  .sec-title.big-title {
    font-size: 30px;
  }
}

/*------------------- 3.4. Common -------------------*/
.image-scale-hover {
  overflow: hidden;
}

.image-scale-hover img {
  transition: all ease 0.4s;
  transform: scale(1.001);
}

.image-scale-hover:hover img {
  transform: scale(1.2);
}

@media not all and (min-resolution: 0.001dpcm) {
  .image-scale-hover:hover img {
    transform: scale(1);
  }
}

.z-index-step1 {
  position: relative;
  z-index: 4 !important;
}

.z-index-common {
  position: relative;
  z-index: 3;
}

.z-index-2 {
  z-index: 2;
}

.z-index-3 {
  z-index: 3;
}

.z-index-n1 {
  z-index: -1;
}

.media-body {
  flex: 1;
}

.badge {
  position: absolute;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  display: inline-block;
  text-align: center;
  background-color: var(--body-color);
  color: var(--white-color);
  padding: 0.25em 0.48em;
  border-radius: 50%;
  top: -5px;
  right: 0;
  font-weight: 400;
}

section {
  position: relative;
}

.section-before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  width: 100%;
  height: auto;
}

.section-before.style-2 {
  top: -10px;
}

.section-after {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  z-index: 2;
  width: 100%;
  height: auto;
}

.section-after.style-2 {
  bottom: -10px;
}

.shape-after {
  position: absolute;
  top: calc(100% - 10px);
  left: 0;
  right: 0;
  z-index: 2;
}

.shape-before {
  position: absolute;
  left: 0;
  right: 0;
  bottom: calc(100% - 8px);
  z-index: 2;
}

.shape-mockup {
  position: absolute;
}

.shape-mockup.z-index-3 {
  z-index: 3;
}

.z-index-negative {
  z-index: -1;
}

.multi-social {
  --icon-size: 45px;
  list-style: none;
}

.multi-social i.fa-facebook-f {
  --theme-color: #3b5997;
}

.multi-social i.fa-twitter {
  --theme-color: #54abf0;
}

.multi-social i.fa-pinterest-p {
  --theme-color: #c8232c;
}

.multi-social i.fa-linkedin-in {
  --theme-color: #0077b5;
}

.multi-social i.fa-vimeo-v {
  --theme-color: #86c9ef;
}

.multi-social i.fa-youtube {
  --theme-color: #ff0000;
}

.multi-social i.fa-instgram {
  --theme-color: #d63084;
}

.multi-social a {
  display: inline-block;
}

.multi-social a i {
  width: var(--icon-size);
  height: var(--icon-size);
  line-height: var(--icon-size);
  text-align: center;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
  margin-right: 7px;
  transition: 0.3s ease-in-out;
}

.multi-social a i:hover {
  background-color: var(--secondary-color);
}

.multi-social a:last-child i {
  margin-right: 0;
}

.check-list ul {
  padding-left: 0;
  list-style: none;
  text-align: left;
  margin-bottom: 0;
  /* Small devices */
}

@media (max-width: 767px) {
  .check-list ul {
    margin-bottom: 12px;
  }
}

.check-list li {
  color: var(--title-color);
  margin-bottom: 10px;
  font-weight: 500;
  position: relative;
  padding-left: 35px;
}

.check-list li:before {
  content: "\f00c";
  font-family: var(--icon-font);
  font-weight: 300;
  width: 25px;
  height: 25px;
  line-height: 25px;
  line-height: 28px;
  margin-right: 11px;
  background-color: var(--secondary-color);
  color: var(--white-color);
  border-radius: 50px;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  position: absolute;
  top: 0.5px;
  left: 0;
}

.check-list.style-2 li {
  padding-left: 25px;
  color: var(--body-color);
}

.check-list.style-2 li:before {
  content: "\f058";
  height: 0;
  width: 0;
  font-weight: 900;
  color: var(--theme-color);
  border: none;
  top: -2px;
}

.check-list.style-3 li {
  padding-left: 45px;
  margin-bottom: 13px;
}

.check-list.style-3 li:before {
  border: none;
  background-color: var(--white-color);
  color: var(--theme-color);
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-shadow: 0px 8px 29px 0px rgba(255, 122, 91, 0.15);
  top: -2px;
}

.check-list.style-4 {
  margin-top: 25px;
  margin-bottom: 25px;
}

.check-list.style-4 ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 8px;
}

.check-list.style-4 li {
  padding-left: 30px;
  color: var(--body-color);
  margin-bottom: 0;
}

.check-list.style-4 li:before {
  content: "\f058";
  margin-right: 11px;
  height: 0;
  width: 0;
  font-weight: 900;
  color: var(--theme-color);
  font-size: 18px;
  display: inline-block;
  border: none;
  position: absolute;
  top: 0.5px;
  left: 0;
}

.two-btns .vs-btn:first-child {
  margin-right: 15px;
}

.slider-shadow .slick-list {
  padding-bottom: 50px;
  margin-bottom: -50px;
  padding-top: 40px;
  margin-top: -40px;
}

.shape-mockup-wrap {
  position: relative;
}

.fa,
.fab,
.fad,
.fal,
.far,
.fas {
  line-height: 1.6;
}

.d-hd-block {
  display: block !important;
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {

  .shape-after img,
  .shape-before img,
  .section-after img,
  .section-before img {
    width: 100%;
  }
}

/* Extra large devices */
@media (max-width: 1680px) {
  .d-hd-block {
    display: none !important;
  }
}

/* Large devices */
@media (max-width: 1199px) {

  .shape-after img,
  .shape-before img,
  .section-after img,
  .section-before img {
    object-fit: cover;
    object-position: center;
    height: 35px;
    width: 100%;
  }

  .space-double .shape-after img,
  .space-double .shape-before img,
  .space-double .section-after img,
  .space-double .section-before img {
    height: 80px;
    width: 100%;
    object-position: center left;
  }

  .space-double .shape-after img,
  .space-double .section-after img {
    object-position: center right;
  }
}

/* Medium devices */
@media (max-width: 991px) {

  .space-double .shape-after img,
  .space-double .shape-before img,
  .space-double .section-after img,
  .space-double .section-before img {
    height: 50px;
  }

  .shape-before {
    bottom: calc(100% - 5px);
  }

  .shape-after {
    top: calc(100% - 5px);
  }

  .section-before {
    top: -5px;
  }

  .section-before.style-2 {
    top: -5px;
  }

  .section-after {
    bottom: -5px;
  }

  .section-after.style-2 {
    bottom: -5px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .check-list.style-4 ul {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .two-btns .vs-btn {
    min-width: 160px;
  }

  .check-list.style-4 ul {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Extra large devices */
@media (max-width: 1680px) {

  .shapePulse,
  .rotate {
    display: none !important;
  }
}

/*------------------- 3.6. Font -------------------*/
.font-icon {
  font-family: var(--icon-font);
}

.font-title {
  font-family: var(--title-font);
}

.font-para {
  font-family: var(--para-font);
}

.font-body {
  font-family: var(--body-font);
}

.fw-light {
  font-weight: 300;
}

.fw-normal {
  font-weight: 400;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold {
  font-weight: 700;
}

.fw-extrabold {
  font-weight: 800;
}

.fs-md {
  font-size: 18px;
}

.fs-xs {
  font-size: 14px;
}

.fs-40 {
  font-size: 40px;
}

/* Large devices */
@media (max-width: 1199px) {
  .fs-40 {
    font-size: 36px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .fs-40 {
    font-size: 32px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .fs-md {
    font-size: 16px;
  }

  .fs-40 {
    font-size: 26px;
  }
}

/*------------------- 3.7. Background -------------------*/
.bg-theme {
  background-color: var(--theme-color) !important;
}

.bg-smoke {
  background-color: var(--smoke-color) !important;
}

.bg-smoke-theme {
  background-color: var(--smoke-theme) !important;
}

.bg-white {
  background-color: var(--white-color) !important;
}

.bg-black {
  background-color: var(--black-color) !important;
}

.bg-title {
  background-color: var(--title-color) !important;
}

.bg-smoke-blue {
  background-color: #dbeaf7;
}

.bg-smoke-theme {
  background-color: #ffe9e4;
}

.bg-smoke-two {
  background-color: #fffdf8;
}

.background-image,
[data-bg-src] {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg-fluid {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.bg-auto {
  background-size: auto auto;
}

/*------------------- 3.8. Text Color -------------------*/
.text-theme {
  color: var(--theme-color) !important;
}

.text-title {
  color: var(--title-color) !important;
}

.text-body {
  color: var(--body-color) !important;
}

.text-light {
  color: var(--light-color) !important;
}

.text-white {
  color: var(--white-color) !important;
}

.text-yellow {
  color: var(--yellow-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-blue {
  color: var(--blue-color) !important;
}

.text-error {
  color: var(--error-color) !important;
}

.text-inherit {
  color: inherit;
}

.text-inherit:hover {
  color: var(--theme-color);
}

a.text-theme:hover,
.text-reset:hover {
  text-decoration: underline;
}

.text-title a,
a.text-title {
  color: var(--title-color);
}

.text-title a:hover,
a.text-title:hover {
  color: var(--theme-color) !important;
}

.text-white a,
a.text-white {
  color: var(--white-color);
}

.text-white a:hover,
a.text-white:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/*------------------- 3.9. Overlay -------------------*/
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

[data-overlay] {
  position: relative;
}

[data-overlay] [class^=col-],
[data-overlay] [class*=col-] {
  z-index: 1;
}

[data-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

[data-overlay=theme]:before {
  background-color: var(--theme-color);
}

[data-overlay=secondary]:before {
  background-color: var(--secondary-color);
}

[data-overlay=title]:before {
  background-color: var(--title-color);
}

[data-overlay=white]:before {
  background-color: var(--white-color);
}

[data-overlay=black]:before {
  background-color: var(--black-color);
}

[data-overlay=custom]:before {
  background-color: #03252d;
}

[data-opacity="1"]:before {
  opacity: 0.1;
}

[data-opacity="2"]:before {
  opacity: 0.2;
}

[data-opacity="3"]:before {
  opacity: 0.3;
}

[data-opacity="4"]:before {
  opacity: 0.4;
}

[data-opacity="5"]:before {
  opacity: 0.5;
}

[data-opacity="6"]:before {
  opacity: 0.6;
}

[data-opacity="7"]:before {
  opacity: 0.7;
}

[data-opacity="8"]:before {
  opacity: 0.8;
}

[data-opacity="9"]:before {
  opacity: 0.9;
}

[data-opacity="10"]:before {
  opacity: 1;
}

/*------------------- 3.10. Animation -------------------*/
.ripple-animation,
.play-btn.style-1::before,
.play-btn.style-1::after {
  animation-duration: var(--ripple-ani-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-name: ripple;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0;
  }

  30% {
    opacity: 0.4;
  }

  100% {
    transform: scale(1.7);
    opacity: 0;
  }
}

.scalein.vs-animated {
  --animation-name: scalein;
}

.slidetopleft.vs-animated {
  --animation-name: slidetopleft;
}

.slidebottomright.vs-animated {
  --animation-name: slidebottomright;
}

.slideinleft.vs-animated {
  --animation-name: slideinleft;
}

.slideinright.vs-animated {
  --animation-name: slideinright;
}

.slideinup.vs-animated {
  --animation-name: slideinup;
}

.slideindown.vs-animated {
  --animation-name: slideindown;
}

.rollinleft.vs-animated {
  --animation-name: rollinleft;
}

.rollinright.vs-animated {
  --animation-name: rollinright;
}

.scalein,
.slidetopleft,
.slidebottomright,
.slideinleft,
.slideinright,
.slideindown,
.slideinup,
.rollinleft,
.rollinright {
  opacity: 0;
  animation-fill-mode: both;
  animation-iteration-count: 1;
  animation-duration: 1s;
  animation-delay: 0.3s;
  animation-name: var(--animation-name);
}

.vs-animated {
  opacity: 1;
}

@keyframes slideinup {
  0% {
    opacity: 0;
    transform: translateY(70px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes slideinright {
  0% {
    opacity: 0;
    transform: translateX(70px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slideindown {
  0% {
    opacity: 0;
    transform: translateY(-70px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes slideinleft {
  0% {
    opacity: 0;
    transform: translateX(-70px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slidebottomright {
  0% {
    opacity: 0;
    transform: translateX(100px) translateY(100px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes slidetopleft {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes scalein {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes rollinleft {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
  }

  to {
    transform: translateX(0) rotate(0deg);
  }
}

@keyframes rollinright {
  0% {
    opacity: 0;
    transform: translateX(100%) rotate(120deg);
  }

  to {
    transform: translateX(0) rotate(0deg);
  }
}

.shapePulse {
  animation: shapePluse 6s linear infinite;
}

@keyframes shapePluse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.rotate {
  animation: rotateShape 10s linear infinite;
}

@keyframes rotateShape {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.moving {
  animation: moving 8s linear infinite;
}

@keyframes moving {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-50px);
  }

  100% {
    transform: translateY(0);
  }
}

.movingX {
  animation: movingX 8s linear infinite;
}

@keyframes movingX {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(-50px);
  }

  100% {
    transform: translateX(0);
  }
}

.movingX-reverse {
  animation: movingXreverse 8s linear infinite;
}

@keyframes movingXreverse {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(50px);
  }

  100% {
    transform: translateX(0);
  }
}

.movingTopLeft {
  animation: movingTopLeft 10s linear infinite;
}

@keyframes movingTopLeft {
  0% {
    opacity: 1;
    transform: translate(0, 0);
  }

  100% {
    opacity: 0;
    transform: translate(-200px, -200px);
  }
}

.movingBottomLeft {
  animation: movingBottomLeft 30s linear infinite;
}

@keyframes movingBottomLeft {
  0% {
    opacity: 1;
    transform: translate(0, 0);
  }

  100% {
    opacity: 0;
    transform: translate(200px, 0px);
  }
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

/*=================================
    04. Template Style
==================================*/
/*------------------- 4.1. Widget  -------------------*/
.widget_nav_menu ul,
.widget_meta ul,
.widget_pages ul,
.widget_archive ul,
.widget_categories ul {
  list-style: none;
  margin: 0 0 -10px 0;
  padding: 0;
}

.widget_nav_menu a,
.widget_meta a,
.widget_pages a,
.widget_archive a,
.widget_categories a {
  position: relative;
  border: 2px solid var(--border-color);
  display: block;
  font-size: 16px;
  color: var(--body-color);
  font-weight: 400;
  background-color: transparent;
  line-height: 1;
  margin-bottom: 9px;
  padding: 19px 25px;
  border-radius: 9999px;
}

.widget_nav_menu a:before,
.widget_meta a:before,
.widget_pages a:before,
.widget_archive a:before,
.widget_categories a:before {
  content: "\f138";
  font-family: var(--icon-font);
  font-weight: 900;
  margin-right: 10px;
  color: var(--theme-color);
  position: relative;
  top: 1px;
  transition: 0.3s ease-in-out;
}

.widget_nav_menu a:hover,
.widget_meta a:hover,
.widget_pages a:hover,
.widget_archive a:hover,
.widget_categories a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.widget_nav_menu a:hover:before,
.widget_meta a:hover:before,
.widget_pages a:hover:before,
.widget_archive a:hover:before,
.widget_categories a:hover:before {
  color: var(--white-color);
}

.widget_nav_menu li,
.widget_meta li,
.widget_pages li,
.widget_archive li,
.widget_categories li {
  display: block;
  position: relative;
}

.widget_nav_menu li span,
.widget_meta li span,
.widget_pages li span,
.widget_archive li span,
.widget_categories li span {
  position: absolute;
  top: 15px;
  right: 25px;
  color: var(--white-color);
  background-color: var(--theme-color);
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 50%;
  line-height: 31px;
  font-size: 14px;
  transition: 0.3s ease-in-out;
}

.widget_nav_menu li:hover>span,
.widget_meta li:hover>span,
.widget_pages li:hover>span,
.widget_archive li:hover>span,
.widget_categories li:hover>span {
  color: var(--title-color);
  background-color: var(--white-color);
}

.widget_nav_menu .children,
.widget_meta .children,
.widget_pages .children,
.widget_archive .children,
.widget_categories .children {
  margin-left: 10px;
  margin-top: 0;
}

.widget_nav_menu a,
.widget_meta a,
.widget_pages a {
  padding-right: 20px;
}

.widget_nav_menu .sub-menu {
  margin-left: 10px;
}

.wp-block-archives {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}

.wp-block-archives a:not(:hover) {
  color: inherit;
}

.vs-blog ul.wp-block-archives li {
  margin: 5px 0;
}

.widget {
  --border-color: rgb(242, 242, 242);
  padding: 40px;
  margin-bottom: 40px;
}

.widget select,
.widget input {
  color: var(--title-color);
  padding-left: 30px;
  font-size: 16px;
  background-color: var(--smoke-color);
  border: none;
  height: 60px;
  border-radius: 50px;
  font-weight: 500;
  width: 100%;
}

.widget select::-moz-placeholder,
.widget input::-moz-placeholder {
  color: var(--title-color);
  opacity: 1;
}

.widget select::-webkit-input-placeholder,
.widget input::-webkit-input-placeholder {
  color: var(--title-color);
  opacity: 1;
}

.widget select:-ms-input-placeholder,
.widget input:-ms-input-placeholder {
  color: var(--title-color);
  opacity: 1;
}

.widget select::placeholder,
.widget input::placeholder {
  color: var(--title-color);
  opacity: 1;
}

.widget_title {
  font-size: 24px;
  line-height: 1em;
  margin-bottom: 25px;
  margin-top: -0.1em;
  font-weight: 700;
  position: relative;
  padding-bottom: 15px;
}

.widget_title::after {
  content: "";
  width: 60px;
  height: 2px;
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--theme-color);
}

.widget .search-form {
  position: relative;
}

.widget .search-form input {
  padding-right: 50px;
}

.widget .search-form button {
  text-align: center;
  padding: 0;
  color: var(--title-color);
  background-color: transparent;
  border: none;
  position: absolute;
  right: 30px;
  line-height: 1;
  top: 50%;
  transform: translateY(-50%);
}

.widget .search-form button:hover {
  color: var(--theme-color);
}

.wp-block-tag-cloud a,
.tagcloud a {
  display: inline-block;
  border: none;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  padding: 9.5px 23px;
  margin-right: 5px;
  margin-bottom: 10px;
  color: var(--theme-color);
  background-color: var(--tag-bg, var(--smoke-theme));
  border-radius: 9999px;
  font-weight: 400;
}

.wp-block-tag-cloud a:hover,
.tagcloud a:hover {
  background-color: var(--theme-color);
  color: var(--white-color) !important;
  box-shadow: none;
}

.tagcloud {
  margin-right: -5px;
  margin-bottom: -10px;
}

.widget .tagcloud a:not(:hover) {
  color: var(--body-color);
  background-color: var(--white-color);
  box-shadow: 0px 5px 15px 0px rgba(0, 19, 87, 0.06);
}

.widget.widget_banner {
  padding: 0;
}

.widget.widget_banner .content {
  text-align: center;
  position: absolute;
  width: 100%;
  top: 55px;
  left: 0;
}

.widget.widget_banner .banner-btn {
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.recent-post:last-child {
  margin-bottom: 0;
}

.recent-post .post-title {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 5px;
  color: var(--title-color);
  line-height: 24px;
}

.recent-post .post-title a {
  color: inherit;
}

.recent-post .post-title a:hover {
  color: var(--theme-color);
}

.recent-post .media-img {
  margin-right: 17px;
  width: 95px;
  overflow: hidden;
}

.recent-post .media-img img {
  transition: all ease 0.4s;
  transform: scale(1);
}

.recent-post .recent-post-meta {
  line-height: 1.8;
  margin-bottom: 3px;
}

.recent-post .recent-post-meta a {
  text-transform: capitalize;
  margin-right: 15px;
  font-size: 14px;
  color: #8f959b;
}

.recent-post .recent-post-meta a:hover {
  color: var(--theme-color);
}

.recent-post .recent-post-meta a i {
  margin-right: 5px;
  font-size: 12px;
}

.recent-post .recent-post-meta a:last-child {
  margin-right: 0;
}

.recent-post:hover .media-img img {
  transform: scale(1.13);
}

@media not all and (min-resolution: 0.001dpcm) {
  .recent-post:hover .media-img img {
    transform: scale(1);
  }
}

.about-logo img {
  max-width: 220px;
}

.sidebar-area ul.wp-block-latest-posts {
  margin-bottom: 0;
}

.sidebar-area ul.wp-block-latest-posts li:last-child {
  margin-bottom: 0;
}

.sidebar-area .widget {
  border: 2px solid var(--border-color);
  position: relative;
  background-color: var(--white-color);
  border-radius: 30px;
  overflow: hidden;
}

.sidebar-area .widget:last-child {
  margin-bottom: 0;
}

.sidebar-area .recent-post {
  border-bottom: 1px solid #ecedf0;
  padding-bottom: 15px;
}

.sidebar-area .recent-post:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.sidebar-area .recent-post .media-img {
  margin-right: 20px;
  width: 90px;
  height: 80px;
  border-radius: 20px;
  overflow: hidden;
}

.sidebar-area .recent-post .media-img img {
  border-radius: 20px;
}

.sidebar-area .recent-post .post-title {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 0;
  color: var(--title-color);
  line-height: 20px;
}

.sidebar-area .recent-post .recent-post-meta a {
  font-size: 14px;
}

.sidebar-area .bg {
  border-radius: 20px;
  background-color: rgb(227, 227, 227);
  position: absolute;
  left: 1262px;
  top: 1523px;
  width: 90px;
  height: 80px;
  z-index: 127;
}

.sidebar-area .widget .wp-block-search {
  margin-bottom: 0;
}

.sidebar-area .wp-block-group__inner-container h2 {
  font-size: 20px;
  line-height: 1em;
  margin-bottom: 20px;
  margin-top: -0.07em;
}

.sidebar-area ol.wp-block-latest-comments {
  padding: 0;
  margin: 0;
}

.sidebar-area ol.wp-block-latest-comments li {
  line-height: 1.5;
  margin: 0 0 20px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
}

.sidebar-area ol.wp-block-latest-comments li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

/* Large devices */
@media (max-width: 1199px) {
  .widget {
    padding: 33px 30px;
  }
}

/* Medium devices */
@media (max-width: 991px) {

  .wp-block-tag-cloud a,
  .tagcloud a {
    padding: 10.5px 18px;
  }

  .sidebar-area {
    padding-top: 30px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .widget {
    padding: 30px 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .widget {
    padding: 30px 20px;
  }

  .widget.widget_banner .content {
    top: 25px;
  }

  .widget.widget_banner .banner-btn {
    bottom: 30px;
  }
}

.class-sidebar .widget,
.service-sidebar .widget {
  padding: 0;
  border: none;
  border-radius: 0;
  background-color: transparent;
}

.class-sidebar .widget .widget_title,
.service-sidebar .widget .widget_title {
  position: relative;
  padding-bottom: 12px;
}

.class-sidebar .widget .widget_title::after,
.service-sidebar .widget .widget_title::after {
  content: "";
  height: 2px;
  width: 40px;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 0;
  left: 0;
}

.widget .banner-2 img {
  border-radius: 30px;
}

ul.popular-service-wrap {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

ul.popular-service-wrap li {
  color: var(--body-color);
  position: relative;
  border-bottom: 1px solid rgb(249, 237, 234);
}

ul.popular-service-wrap li a {
  color: inherit;
  padding: 10px 0;
  display: block;
}

ul.popular-service-wrap li a::after {
  content: "\f178";
  font-family: var(--icon-font);
  font-weight: 300;
  float: right;
}

ul.popular-service-wrap li:last-child {
  border-bottom: none;
}

ul.popular-service-wrap li:last-child a {
  padding-bottom: 0;
}

ul.popular-service-wrap li:first-child a {
  padding-top: 0;
}

ul.popular-service-wrap li:hover {
  color: var(--theme-color);
}

.class-info-wrap,
.author-widget-wrap {
  background-color: var(--smoke-color);
  border-radius: 30px;
  padding: 45px 50px;
}

.info-item {
  display: flex;
  border-bottom: 1px solid #ececec;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.info-item i {
  color: var(--theme-color);
  font-size: 30px;
  margin-right: 22px;
}

.info-item .title {
  margin-bottom: 3px;
  font-size: 18px;
}

.info-item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.author-widget-wrap {
  padding: 50px 50px 45px 50px;
  text-align: center;
}

.author-widget-wrap .author-info {
  margin-bottom: 15px;
}

.author-widget-wrap .avator {
  width: 150px;
  height: 150px;
  margin: 0 auto 25px auto;
}

.author-widget-wrap .avator img {
  border-radius: 50%;
}

.author-widget-wrap .name {
  margin-bottom: 0;
}

.author-widget-wrap .author-bio {
  margin-bottom: 25px;
}

.author-social a i {
  margin-right: 5px;
}

/* Extra small devices */
@media (max-width: 416px) {
  .class-info-wrap {
    padding: 25px 25px;
  }

  .author-widget-wrap {
    padding: 30px 25px 25px 25px;
  }
}

.widget_shopping_cart {
  padding: 0;
  margin-bottom: 0;
}

.widget_shopping_cart .widget_title {
  margin-bottom: 10px;
}

.widget_shopping_cart ul {
  margin: 0;
  padding: 0;
}

.widget_shopping_cart ul li {
  list-style-type: none;
}

.widget_shopping_cart .buttons {
  margin-bottom: 0;
}

.widget_shopping_cart .mini_cart_item {
  position: relative;
  padding: 30px 10px 30px 90px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  text-align: left;
}

.widget_shopping_cart .mini_cart_item:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.widget_shopping_cart .cart_list a:not(.remove) {
  display: block;
  color: var(--body-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  font-weight: 600;
  color: var(--title-color);
}

.widget_shopping_cart .cart_list a:not(.remove):hover {
  color: var(--theme-color);
}

.widget_shopping_cart .cart_list a.remove {
  position: absolute;
  top: 50%;
  left: 95%;
  transform: translateY(-50%);
  color: var(--body-color);
}

.widget_shopping_cart .cart_list a.remove:hover {
  color: var(--theme-color);
}

.widget_shopping_cart .cart_list img {
  width: 75px;
  height: 75px;
  position: absolute;
  left: 0;
  top: 20px;
  border: 1px solid var(--border-color);
}

.widget_shopping_cart .quantity {
  display: inline-flex;
  white-space: nowrap;
  vertical-align: top;
  margin-right: 20px;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color);
  border: none;
}

.widget_shopping_cart .total {
  margin-top: 20px;
  font-size: 18px;
  color: var(--title-color);
}

.widget_shopping_cart .amount {
  font-size: 16px;
  padding-left: 5px;
  font-weight: 600;
  font-family: var(--title-font);
}

.widget_shopping_cart .vs-btn {
  margin-right: 15px;
}

.widget_shopping_cart .vs-btn:last-child {
  margin-right: 0;
}

.sidebar-area .widget_shopping_cart .vs-btn {
  margin-right: 10px;
  padding: 8px 22px;
  font-size: 14px;
}

.shop-sidebar .widget {
  padding: 0;
  border: none;
  border-radius: 0;
  padding-bottom: 40px;
  border-bottom: 1px solid #f9edea;
  overflow: visible;
}

.shop-sidebar .widget:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.shop-sidebar .widget .widget_title {
  padding-bottom: 15px;
  position: relative;
}

.shop-sidebar .widget .widget_title:after {
  content: "";
  height: 2px;
  width: 40px;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 0;
  left: 0;
}

.shop-sidebar .widget .tagcloud a:not(:hover) {
  color: var(--theme-color);
  background-color: var(--smoke-theme);
  box-shadow: none;
}

.shop-sidebar .widget_nav_menu ul,
.shop-sidebar .widget_meta ul,
.shop-sidebar .widget_pages ul,
.shop-sidebar .widget_archive ul,
.shop-sidebar .widget_categories ul {
  margin-bottom: 0;
}

.shop-sidebar .widget_nav_menu a,
.shop-sidebar .widget_meta a,
.shop-sidebar .widget_pages a,
.shop-sidebar .widget_archive a,
.shop-sidebar .widget_categories a {
  font-family: var(--body-font);
  position: relative;
  border: none;
  font-size: 16px;
  font-weight: 400;
  background-color: transparent;
  line-height: 1;
  margin-bottom: 16px;
  padding: 0;
  border-radius: 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #f9edea;
}

.shop-sidebar .widget_nav_menu a:before,
.shop-sidebar .widget_meta a:before,
.shop-sidebar .widget_pages a:before,
.shop-sidebar .widget_archive a:before,
.shop-sidebar .widget_categories a:before {
  display: none;
}

.shop-sidebar .widget_nav_menu a:hover,
.shop-sidebar .widget_meta a:hover,
.shop-sidebar .widget_pages a:hover,
.shop-sidebar .widget_archive a:hover,
.shop-sidebar .widget_categories a:hover {
  color: var(--theme-color);
  background-color: transparent;
  border-color: #f9edea;
}

.shop-sidebar .widget_nav_menu a:hover:before,
.shop-sidebar .widget_meta a:hover:before,
.shop-sidebar .widget_pages a:hover:before,
.shop-sidebar .widget_archive a:hover:before,
.shop-sidebar .widget_categories a:hover:before {
  color: var(--white-color);
}

.shop-sidebar .widget_nav_menu li,
.shop-sidebar .widget_meta li,
.shop-sidebar .widget_pages li,
.shop-sidebar .widget_archive li,
.shop-sidebar .widget_categories li {
  display: block;
  position: relative;
}

.shop-sidebar .widget_nav_menu li span,
.shop-sidebar .widget_meta li span,
.shop-sidebar .widget_pages li span,
.shop-sidebar .widget_archive li span,
.shop-sidebar .widget_categories li span {
  font-family: var(--body-font);
  height: auto;
  width: auto;
  position: absolute;
  top: -7px;
  right: 0;
  color: var(--body-color);
  background-color: transparent;
  font-size: 16px;
  transition: 0.3s ease-in-out;
}

.shop-sidebar .widget_nav_menu li:hover>span,
.shop-sidebar .widget_meta li:hover>span,
.shop-sidebar .widget_pages li:hover>span,
.shop-sidebar .widget_archive li:hover>span,
.shop-sidebar .widget_categories li:hover>span {
  color: var(--theme-color);
  background-color: transparent;
}

.shop-sidebar .widget_nav_menu li:last-child a,
.shop-sidebar .widget_meta li:last-child a,
.shop-sidebar .widget_pages li:last-child a,
.shop-sidebar .widget_archive li:last-child a,
.shop-sidebar .widget_categories li:last-child a {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.shop-sidebar .widget_nav_menu .children,
.shop-sidebar .widget_meta .children,
.shop-sidebar .widget_pages .children,
.shop-sidebar .widget_archive .children,
.shop-sidebar .widget_categories .children {
  margin-left: 10px;
  margin-top: 0;
}

.price_slider_wrapper .price_label span {
  display: inline-block;
  color: var(--body-color);
}

.price_slider_wrapper .ui-slider {
  height: 8px;
  position: relative;
  width: 100%;
  background-color: #fff2ef;
  border: none;
  margin-top: 10px;
  margin-bottom: 20px;
  cursor: pointer;
  border-radius: 3px;
}

.price_slider_wrapper .ui-slider-range {
  border: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 1;
  display: block;
  background-color: var(--theme-color);
}

.price_slider_wrapper .ui-slider-handle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-top: -2px;
  text-align: center;
  line-height: 10.5px;
  padding: 0;
  border: none;
  cursor: pointer;
  position: absolute;
  margin-top: -4px;
  z-index: 2;
  box-shadow: 0px 8px 13px 0px rgba(255, 79, 38, 0.21);
  background-color: var(--theme-color);
  transform: translateX(-1px);
}

.price_slider_wrapper .ui-slider-handle:focus {
  outline: none;
  box-shadow: 1.5px 2.598px 10px 0px rgba(0, 0, 0, 0.15);
}

.price_slider_wrapper .ui-slider-handle:before {
  content: "";
  position: absolute;
  background-color: #fff;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.price_slider_wrapper .ui-slider-handle:last-child {
  transform: translateX(-15px);
}

.price_slider_wrapper button,
.price_slider_wrapper .button {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-weight: 500;
  line-height: 1.6;
  text-transform: capitalize;
  text-align: center;
  border-radius: 50px;
  border: none;
  display: inline-block;
  overflow: hidden;
  position: relative;
  z-index: 2;
  padding: 7px 20px;
  min-width: 100px;
  font-size: 16px;
  transition: 0.4s ease-in;
}

.price_slider_wrapper button:hover,
.price_slider_wrapper .button:hover {
  background-color: var(--title-color);
}

.product_list_widget {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.product_list_widget .recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 0;
  border-bottom: none;
}

.product_list_widget .recent-post:last-child {
  margin-bottom: 0;
}

.product_list_widget .recent-post .media-img {
  width: 80px;
  margin-right: 16px;
}

.product_list_widget .recent-post .media-img img {
  border-radius: 20px;
}

.product_list_widget .recent-post-title {
  margin-bottom: 3px;
}

.product_list_widget .recent-post-title a {
  color: inherit;
}

.product_list_widget .recent-post-title a:hover {
  color: var(--theme-color);
}

.product_list_widget .star-rating {
  font-size: 12px;
  margin-bottom: 3px;
}

.footer-widget {
  margin-bottom: 40px;
}

.footer-widget,
.footer-widget .widget {
  padding: 0;
  border: none;
  padding-bottom: 0;
  background-color: transparent;
}

.footer-widget .widget_title {
  position: relative;
  border-left: none;
  font-family: var(--title-font);
  font-size: 24px;
  margin-top: -0.15em;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: none;
  font-weight: bold;
  position: relative;
}

.footer-widget .widget_title:after {
  content: "";
  height: 1px;
  width: 50px;
  background-color: var(--title-color);
  position: absolute;
  bottom: 0;
  left: 0;
}

.footer-widget.widget_meta,
.footer-widget.widget_pages,
.footer-widget.widget_archive,
.footer-widget.widget_categories,
.footer-widget.widget_nav_menu {
  margin-bottom: 45px;
}

.footer-widget.widget_meta ul,
.footer-widget.widget_pages ul,
.footer-widget.widget_archive ul,
.footer-widget.widget_categories ul,
.footer-widget.widget_nav_menu ul {
  margin-top: 0;
}

.footer-widget.widget_meta .widget_title,
.footer-widget.widget_pages .widget_title,
.footer-widget.widget_archive .widget_title,
.footer-widget.widget_categories .widget_title,
.footer-widget.widget_nav_menu .widget_title {
  margin-bottom: 25px;
}

.footer-widget.widget_meta a,
.footer-widget.widget_pages a,
.footer-widget.widget_archive a,
.footer-widget.widget_categories a,
.footer-widget.widget_nav_menu a {
  background-color: transparent;
  padding: 0;
  position: relative;
  margin-bottom: 18px;
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  border: none;
  transition: all ease 0.4s;
}

.footer-widget.widget_meta a:before,
.footer-widget.widget_pages a:before,
.footer-widget.widget_archive a:before,
.footer-widget.widget_categories a:before,
.footer-widget.widget_nav_menu a:before {
  content: "\f105";
  font-family: var(--icon-font);
  margin-right: 10px;
  font-weight: 300;
  color: var(--body-color);
  transition: all ease 0.4s;
}

.footer-widget.widget_meta a:hover,
.footer-widget.widget_pages a:hover,
.footer-widget.widget_archive a:hover,
.footer-widget.widget_categories a:hover,
.footer-widget.widget_nav_menu a:hover {
  background-color: transparent;
  color: var(--theme-color);
}

.footer-widget.widget_meta a:hover::before,
.footer-widget.widget_pages a:hover::before,
.footer-widget.widget_archive a:hover::before,
.footer-widget.widget_categories a:hover::before,
.footer-widget.widget_nav_menu a:hover::before {
  color: var(--theme-color);
}

.footer-widget.widget_meta li>span,
.footer-widget.widget_pages li>span,
.footer-widget.widget_archive li>span,
.footer-widget.widget_categories li>span,
.footer-widget.widget_nav_menu li>span {
  width: auto;
  height: auto;
  position: relative;
  background-color: transparent;
  color: var(--body-color);
  line-height: 1;
}

.footer-widget.widget_meta li:last-child a,
.footer-widget.widget_pages li:last-child a,
.footer-widget.widget_archive li:last-child a,
.footer-widget.widget_categories li:last-child a,
.footer-widget.widget_nav_menu li:last-child a {
  margin-bottom: 0;
}

.footer-widget .about-text {
  margin-top: -14px;
}

.footer-widget .recent-post {
  max-width: 280px;
}

.footer-widget .vs-widget-contact {
  max-width: 240px;
  margin-top: -4px;
}

.footer-layout2 .footer-widget {
  margin-bottom: 40px;
}

.footer-layout2 .footer-widget .widget_title {
  color: var(--white-color);
}

.footer-layout2 .footer-widget .widget_title:after {
  background-color: var(--theme-color);
  height: 2px;
}

.footer-layout2 .footer-widget p {
  color: var(--white-color);
}

.footer-layout2 .footer-widget.widget_meta,
.footer-layout2 .footer-widget.widget_pages,
.footer-layout2 .footer-widget.widget_archive,
.footer-layout2 .footer-widget.widget_categories,
.footer-layout2 .footer-widget.widget_nav_menu {
  margin-bottom: 45px;
}

.footer-layout2 .footer-widget.widget_meta a,
.footer-layout2 .footer-widget.widget_pages a,
.footer-layout2 .footer-widget.widget_archive a,
.footer-layout2 .footer-widget.widget_categories a,
.footer-layout2 .footer-widget.widget_nav_menu a {
  color: var(--light-color);
}

.footer-layout2 .footer-widget.widget_meta a:hover,
.footer-layout2 .footer-widget.widget_pages a:hover,
.footer-layout2 .footer-widget.widget_archive a:hover,
.footer-layout2 .footer-widget.widget_categories a:hover,
.footer-layout2 .footer-widget.widget_nav_menu a:hover {
  color: var(--secondary-color);
}

.footer-layout2 .footer-widget.widget_meta a:hover:before,
.footer-layout2 .footer-widget.widget_pages a:hover:before,
.footer-layout2 .footer-widget.widget_archive a:hover:before,
.footer-layout2 .footer-widget.widget_categories a:hover:before,
.footer-layout2 .footer-widget.widget_nav_menu a:hover:before {
  color: var(--secondary-color);
}

.footer-layout2 .footer-widget .recent-post .media-img {
  border-radius: 15px;
  overflow: hidden;
}

.footer-layout2 .footer-widget .recent-post .media-img img {
  border-radius: 15px;
}

.footer-layout2 .footer-widget .recent-post .recent-post-meta a:hover {
  color: var(--secondary-color);
}

.footer-layout2 .footer-widget .recent-post .post-title {
  color: var(--smoke-color);
}

.footer-layout2 .footer-widget .recent-post .post-title a {
  color: inherit;
}

.footer-layout2 .footer-widget .recent-post .post-title a:hover {
  color: var(--secondary-color);
}

.vs-widget-about {
  max-width: 270px;
}

/* Large devices */
/* Medium devices */
@media (max-width: 991px) {
  .footer-widget .widget_title {
    margin-bottom: 18px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .vs-widget-about {
    text-align: left;
    margin: unset;
    max-width: 450px;
  }
}

/*------------------- 4.2. Header  -------------------*/
.vs-header {
  position: relative;
}

.header-menu-area {
  position: relative;
  z-index: 3;
}

.header-top-area {
  background-color: #fff;
  color: var(--white-color);
  padding-top: 12px;
  padding-bottom: 12px;
  /*
  line-height: 1;
  */
}

.header-top-area.style-2 {
  background-color: var(--white-color);
  color: var(--body-color);
  border-bottom: 1px solid rgb(239, 239, 239);
}

.sticky-wrapper.will-sticky {
  position: relative;
  z-index: 999;
}

.sticky-wrapper.will-sticky .sticky-active .header-menu-area.v4 {
  background-color: var(--white-color);
  /* border-bottom: 2px solid var(--theme-color); */
}

.header-top-area.v4 .container {
  max-width: 1250px;
}

.header-top-area.v4 .header-social ul li {
  margin-right: 10px;
}

.header-top-area.v4 .header-social ul li a {
  background-color: var(--white-color);
  width: 36px;
  height: 36px;
  line-height: 40px;
  border-radius: 50%;
  text-align: center;
  color: var(--theme-color);
}

.header-top-area.v4 .header-social ul li a:hover {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.header-menu-area.v4 {
  background-color: #fff;
  z-index: revert-layer;
}

.header-menu-area.v4 .container {
  max-width: 1370px;
  margin: 0 auto;
}

.header-menu-area.v4 .lower-header {
  position: relative;
  z-index: 999;
  background-color: var(--white-color);
  padding: 0 20px;
  border-radius: 6px;
  /* Medium devices */
}

@media (max-width: 991px) {
  .header-menu-area.v4 .lower-header {
    padding: 0 15px;
  }
}

.header-menu-area.v4 .lower-header .main-menu ul li {
  padding: 10px 4px;
  text-align: center;
}

.header-menu-area.v4 .lower-header .main-menu ul li .sub-menu li {
  padding: 3px 9px;
}

.header-links {
  display: inline-block;
}

.header-links ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-links ul li {
  display: inline-block;
  position: relative;
  font-weight: 500;
  margin-left: 20px;
  padding-left: 20px;
  font-family: var(--body-font);
}

.header-links ul li:before {
  content: "";
  height: 15px;
  width: 1px;
  position: absolute;
  top: 4px;
  left: 0;
  background-color: rgba(255, 255, 255, 0.6);
}

.header-links ul li:first-child {
  margin-left: 0;
  padding-left: 0;
}

.header-links ul li:first-child:before {
  display: none;
}

.header-links ul li>i {
  margin-right: 15px;
}

.header-links a {
  color: var(--white-color);
}

.header-links a:hover {
  color: var(--secondary-color);
}

.header-links.style-3 ul {
  display: flex;
  align-items: center;
}

.header-links.style-3 li:before {
  top: 0;
}

.header-notice {
  display: flex;
  align-items: center;
}

.header-notice .date {
  margin-right: 20px;
  padding-right: 20px;
  position: relative;
}

.header-notice .date:after {
  content: "";
  height: 20px;
  width: 1px;
  position: absolute;
  top: 5px;
  right: 0;
  background-color: rgba(255, 255, 255, 0.8);
}

.header-notice .day {
  font-size: 30px;
  font-weight: bold;
  margin-right: 11px;
}

.header-notice .month {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 400;
  width: 25px;
  display: inline-block;
  line-height: 1.1;
  transform: translateY(2px);
}

.header-info {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.4;
}

.header-info a {
  font-size: inherit;
  color: var(--title-color);
  display: block;
}

.header-info a:hover {
  color: var(--theme-color);
}

.header-button {
  display: flex;
  align-items: center;
}

.header-button .vs-btn,
.header-button button {
  margin-right: 10px;
}

.header-button .vs-btn:last-child,
.header-button button:last-child {
  margin-right: 0;
}

.header-button .simple-icon {
  margin-right: 30px;
}

.header-button .simple-icon:last-child {
  margin-right: 0;
}

.vs-menu-wrapper .vs-menu-toggle {
  right: -20px;
  top: 40px;
  width: 40px;
  height: 40px;
  line-height: 42px;
  font-size: 18px;
  text-align: center;
}

.vs-menu-wrapper .vs-menu-toggle i {
  margin-right: 0;
  line-height: 1.6;
}

.vs-menu-toggle {
  height: 40px;
  width: auto;
  padding: 6px 20px 5px 20px;
  border-radius: 50px;
  line-height: 1;
  font-size: 22px;
}

.vs-menu-toggle i {
  margin-right: 5px;
  vertical-align: middle;
  position: relative;
  top: 0;
}

.vs-menu-toggle.simple-icon {
  background-color: transparent;
  color: var(--title-color);
  padding: 0;
}

.will-sticky .sticky-active {
  position: fixed;
  top: -100%;
  right: 0;
  left: 0;
  background-color: var(--white-color);
  transition: all ease 0.8s;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.07);
  z-index: 20;
}

.will-sticky .sticky-active.active {
  top: 0;
}

.header-logo {
  padding: 10px 0;
}

.main-menu a {
  display: block;
  position: relative;
  font-family: var(--body-font);
  font-weight: 500;
  font-size: 18px;
  color: var(--title-color);
}

.main-menu a:hover {
  color: var(--theme-color);
}

.main-menu>ul>li {
  padding: 36px 0;
}

.main-menu>ul>li>a {
  padding: 12px 7px;
  border-radius: 6px;
}

.main-menu>ul>li>a:hover {
  background-color: #136693;
  color: var(--white-color);
}

.main-menu ul {
  margin: 0;
  padding: 0;
  align-items: center;
    display: flex;
}

.main-menu ul li {
  list-style-type: none;
  display: inline-block;
  position: relative;
}

.main-menu ul li.menu-item-has-children>a:after {
  content: "\f107";
  position: relative;
  font-family: var(--icon-font);
  margin-left: 3px;
  top: 0.8px;
  font-size: 0.8rem;
}

.main-menu ul li:last-child {
  margin-right: 0;
}

.main-menu ul li:first-child {
  margin-left: 0;
}

.main-menu ul li:hover>ul.sub-menu,
.main-menu ul li:hover>ul.mega-menu {
  visibility: visible;
  opacity: 1;
  margin-top: 0;
  z-index: 9;
  background-color: rgb(41 37 31 / 94%);
}

.main-menu ul.sub-menu,
.main-menu ul.mega-menu {
  position: absolute;
  text-align: left;
  top: 100%;
  left: 0;
  background-color: var(--white-color);
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
  visibility: hidden;
  min-width: 190px;
  width: max-content;
  padding: 7px;
  left: -14px;
  margin-top: 50px;
  opacity: 0;
  z-index: -1;
  border-bottom: 3px solid var(--theme-color);
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.09), 0px 3px 0px 0px rgba(231, 13, 60, 0.004);
  transform-origin: top center;
  transition: margin-top 0.4s ease-in-out 0s, visibility 0.4s ease-in-out 0s, opacity 0.4s ease-in-out 0s, z-index 0s;
}

.main-menu ul.sub-menu {
  padding: 10px 20px;
  left: -27px;
}

.main-menu ul.sub-menu:before {
  content: "";
  position: absolute;
  left: 34.5px;
  top: 30px;
  width: 1px;
  background-color: #ededed;
  height: calc(100% - 60px);
}

.main-menu ul.sub-menu li {
  display: block;
  margin: 0 0;
  padding: 3px 9px;
}

.main-menu ul.sub-menu li.menu-item-has-children>a:after {
  content: "\f105";
  float: right;
  top: 0;
}

.main-menu ul.sub-menu li a {
  position: relative;
  padding-left: 21px;
  padding: 12px 14px;
}
/*
.main-menu ul.sub-menu li a:before {
  content: "\f111";
  position: absolute;
  top: 2em;
  left: 0;
  font-family: var(--icon-font);
  width: 11px;
  height: 11px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 0.18em;
  line-height: 11px;
  color: var(--theme-color);
  font-weight: 700;
  background-color: var(--white-color);
  box-shadow: inset 0px 2px 4px 0px rgba(173, 136, 88, 0.4);
}
*/

.main-menu ul.sub-menu li a:hover {
  color: #fff;
  padding: 12px 14px;
  border-radius: 6px;
  background-color: #136693;
}

.main-menu ul.sub-menu li ul.sub-menu {
  left: 100%;
  right: auto;
  top: 0;
  margin: 0 0;
  margin-left: 20px;
}

.main-menu ul.sub-menu li ul.sub-menu li ul {
  left: 100%;
  right: auto;
}

.main-menu .mega-menu-wrap {
  position: static;
}

.main-menu ul.mega-menu {
  display: flex;
  justify-content: space-between;
  text-align: left;
  width: 100%;
  max-width: var(--main-container);
  padding: 20px 15px 23px 15px;
  left: 50%;
  transform: translateX(-50%);
}

.main-menu ul.mega-menu li {
  display: block;
  width: 100%;
  padding: 0 15px;
}

.main-menu ul.mega-menu li li {
  padding: 4px 0;
}

.main-menu ul.mega-menu li a {
  display: inline-block;
}

.main-menu ul.mega-menu>li>a {
  display: block;
  padding: 0;
  padding-bottom: 15px;
  margin-bottom: 10px;
  text-transform: capitalize;
  letter-spacing: 1px;
  font-weight: 700;
  color: var(--title-color);
  border-color: var(--theme-color);
}

.main-menu ul.mega-menu>li>a::after,
.main-menu ul.mega-menu>li>a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 15px;
  height: 1px;
  background-color: var(--theme-color);
}

.main-menu ul.mega-menu>li>a::after {
  width: calc(100% - 20px);
  left: 20px;
}

.main-menu ul.mega-menu>li>a:hover {
  padding-left: 0;
}

.menu-style2>ul>li {
  padding: 0;
  margin: 0 17px;
}

.menu-style2>ul>li>a {
  padding: 20px 0;
}

.menu-style2>ul>li>a:hover {
  background-color: transparent;
  color: var(--theme-color);
}

.header-social ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.header-social li {
  display: inline-block;
  margin: 0 20px 0 0;
}

.header-social li:last-child {
  margin-right: 0;
}

.header-social a {
  display: block;
  color: var(--white-color);
}

.header-social a:hover {
  color: var(--secondary-color);
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .main-menu>ul>li>a {
    padding: 5px 0px;
  }

  .header-menu-area.v4 .lower-header .main-menu ul li {
    padding: 24px 4px;
  }

  .header-menu-area.v4 .lower-header {
    padding: 0 4px;
  }

}

/* Large devices */
@media (max-width: 1199px) {
  .header-links ul li {
    margin-left: 15px;
    padding-left: 15px;
  }

  .text-none {
    display: none;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .header-logo {
    max-width: 140px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .will-sticky .header-menu-area.v4 .lower-header {
    padding: 0;
  }
}

/*------------------- 4.3. Footer  -------------------*/
.footer-wrapper .widget-area {
  padding-top: var(--section-space);
  padding-bottom: calc(var(--section-space) - 40px);
}

.footer-layout1 {
  background-size: contain;
  background-position: bottom center;
  background-color: #dbeaf7;
}

.footer-layout1 .widget-area {
  padding-bottom: 300px;
}

.footer-layout1 .recent-post .recent-post-meta a {
  color: var(--theme-color);
}

.footer-layout1 .copyright {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 11px 20px 0px 0px;
  padding: 16px 20px 13px 20px;
  line-height: 1;
}

.footer-layout1 .copyright a:hover {
  color: var(--white-color);
  text-decoration: underline;
}

.footer-layout2 {
  background-color: #232323;
}

.footer-wrapper.four {
  position: relative;
}

.footer-wrapper.four::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(16, 55, 65, 0.6);
  z-index: -1;
}

.footer-wrapper.four .footer-copyright {
  background-color: #103741;
}

.about-logo {
  margin-bottom: 40px;
  margin-top: -8px;
}

.about-text {
  margin-bottom: 25px;
}

.footer-info {
  position: relative;
  min-height: 40px;
  align-items: center;
  display: flex;
  padding: 0 0 0 50px;
}

.footer-info a {
  color: inherit;
}

.footer-info a:hover {
  color: var(--secondary-color);
}

.footer-info i {
  color: var(--white-color);
  margin-right: 10px;
  border: 1px solid transparent;
  background-color: var(--theme-color);
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  text-align: center;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: all ease 0.4s;
}

.footer-info:hover i {
  background-color: var(--secondary-color);
  border-color: transparent;
  color: var(--white-color);
}

.copyright {
  text-align: center;
  margin-bottom: 0;
  color: var(--title-color);
  padding: 27px 0;
}

.copyright a {
  color: inherit;
}

.copyright a:hover {
  color: var(--theme-color);
}

.footer-layout2 .footer-copyright {
  background-color: #1a1a1a;
}

.footer-layout2 .footer-copyright .copyright {
  color: var(--light-color);
}

.footer-layout3 .about-text {
  color: var(--white-color);
}

.footer-layout3 .footer-widget .widget_title {
  color: var(--white-color);
}

.footer-layout3 .footer-widget .widget_title:after {
  background-color: currentColor;
}

.footer-layout3 .footer-widget.widget_nav_menu a {
  color: var(--white-color);
}

.footer-layout3 .footer-widget.widget_nav_menu a:before {
  color: var(--white-color);
}

.footer-layout3 .footer-widget.widget_nav_menu a:hover {
  color: var(--secondary-color);
}

.footer-layout3 .post-title a,
.footer-layout3 .footer-info a,
.footer-layout3 .recent-post-meta a {
  color: var(--white-color);
}

.footer-layout3 .post-title a:hover,
.footer-layout3 .footer-info a:hover,
.footer-layout3 .recent-post-meta a:hover {
  color: var(--secondary-color);
}

.footer-layout3 .footer-info {
  color: var(--white-color);
}

.footer-layout3 .footer-info i {
  color: var(--white-color);
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.5);
  transition: all ease 0.4s;
}

.footer-layout3 .footer-info:hover i {
  border-color: transparent;
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.footer-layout3 .footer-copyright {
  background-color: #232323;
}

.footer-layout3 .copyright {
  color: var(--white-color);
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .footer-layout1 {
    background-size: cover;
  }
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .footer-layout1 .widget-area {
    padding-bottom: 200px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .footer-layout1 .widget-area {
    padding-bottom: 80px;
  }

  .about-logo {
    margin-top: 0;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .copyright {
    padding: 17px 0;
  }

  .footer-wrapper .widget-area {
    padding-top: var(--section-space-mobile);
    padding-bottom: calc(var(--section-space-mobile) - 40px);
  }

  .footer-layout1 .widget-area {
    padding-bottom: 100px;
  }

  .footer-layout1 {
    background-position: bottom 60px center;
  }

  .footer-layout1 .footer-copyright {
    background-color: var(--white-color);
  }
}

/* Small devices */
@media (max-width: 767px) {
  .footer-layout1 .widget-area {
    padding-bottom: 80px;
  }
}

/*------------------- 4.4. Breadcumb  -------------------*/
.breadcumb-wrapper {
  z-index: 2;
  background-color: var(--title-color);
}

.breadcumb-wrapper[data-overlay]::before {
  z-index: -1;
}

.breadcumb-content {
  text-align: center;
  padding-top: 180px;
  padding-bottom: 180px;
}

.breadcumb-menu {
  max-width: 100%;
  margin: 0 0 0 0;
  padding: 0;
  list-style-type: none;
}

.breadcumb-menu li {
  display: inline-block;
  margin-right: 3px;
  padding-right: 2px;
  list-style: none;
  position: relative;
}

.breadcumb-menu li:after {
  content: "/";
  margin-left: 10px;
}

.breadcumb-menu li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.breadcumb-menu li:last-child:after {
  display: none;
}

.breadcumb-menu li,
.breadcumb-menu a,
.breadcumb-menu span {
  white-space: normal;
  color: inherit;
  word-break: break-word;
  font-weight: 500;
  font-size: 24px;
  color: var(--white-color);
}

.breadcumb-menu li i,
.breadcumb-menu a i,
.breadcumb-menu span i {
  margin-right: 10px;
  font-size: 0.8rem;
  position: relative;
  top: -1px;
}

.breadcumb-title {
  font-size: 72px;
  font-weight: 600;
  margin-top: -0.3em;
  color: var(--white-color);
  margin-bottom: 8px;
}

/* Large devices */
@media (max-width: 1199px) {
  .breadcumb-title {
    font-size: 60px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .breadcumb-content {
    padding-top: 120px;
    padding-bottom: 120px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .breadcumb-wrapper .section-after {
    bottom: -6px;
  }

  .breadcumb-title {
    font-size: 48px;
    margin-bottom: 0;
  }

  .breadcumb-menu li,
  .breadcumb-menu a,
  .breadcumb-menu span {
    font-size: 18px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .breadcumb-wrapper .section-after {
    display: none;
  }

  .breadcumb-title {
    font-size: 36px;
  }

  .breadcumb-content {
    padding-top: var(--section-space-mobile);
    padding-bottom: var(--section-space-mobile);
  }
}

/*------------------- 4.5. Pagination  -------------------*/
.vs-pagination {
  text-align: center;
}

.vs-pagination ul {
  margin: 0;
  padding: 0;
}

.vs-pagination li {
  display: inline-block;
  margin: 0 3px;
  list-style-type: none;
}

.vs-pagination li:last-child {
  margin-right: 0;
}

.vs-pagination li:first-child {
  margin-left: 0;
}

.vs-pagination span,
.vs-pagination a {
  display: inline-block;
  width: 55px;
  height: 55px;
  line-height: 54px;
  text-align: center;
  position: relative;
  background-color: #fff;
  z-index: 1;
  font-weight: 600;
  border-radius: 50%;
  color: var(--theme-color);
  font-size: 20px;
  border: 1px solid var(--theme-color);
  font-weight: 700;
}

.vs-pagination span i,
.vs-pagination a i {
  position: relative;
  top: 0.06em;
  font-size: 16px;
  line-height: 1;
}

.vs-pagination span.active,
.vs-pagination span:hover,
.vs-pagination a.active,
.vs-pagination a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: transparent;
}

/* Small devices */
@media (max-width: 767px) {

  .vs-pagination span,
  .vs-pagination a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }

  .vs-pagination span i,
  .vs-pagination a i {
    font-size: 12px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {

  .vs-pagination span,
  .vs-pagination a {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
}

/*------------------- 4.6. Blog  -------------------*/
.blog-card {
  --gap: 40px;
  margin-bottom: 30px;
}

.blog-card .blog-img {
  max-width: calc(100% - var(--gap) * 2);
  margin-left: var(--gap);
}

.blog-card .blog-content {
  background-color: var(--white-color);
  border-radius: 50px;
  box-shadow: 0px 10px 15px 0px rgba(193, 202, 210, 0.25);
  padding: 145px 40px 33px 40px;
  margin-top: -110px;
  border: 1px solid transparent;
  transition: all ease 0.4s;
}

.blog-card .blog-meta {
  margin-bottom: 10px;
}

.blog-card .blog-title {
  line-height: 1.5;
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.blog-card:hover .blog-img img {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .blog-card:hover .blog-img img {
    transform: scale(1);
  }
}

.blog-card:hover .blog-content {
  box-shadow: none;
  border-color: var(--theme-color);
}

.blog-section-two .mb--30 {
  margin-bottom: -30px;
}

.blog-section-two .vs-btn.style-2 {
  background-color: var(--theme-color);
  width: 179px;
  padding: 11px 30px;
}

.blog-section-two .bulb {
  position: absolute;
  top: 90px;
  right: 140px;
  /* Extra large devices */
}

@media (max-width: 1680px) {
  .blog-section-two .bulb {
    display: none !important;
  }
}

.blog-section-two .cap {
  position: absolute;
  bottom: 36px;
  left: 48px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .blog-section-two .cap {
    display: none !important;
  }
}

.blog-section-two .ms-auto {
  /* Medium devices */
}

@media (max-width: 991px) {
  .blog-section-two .ms-auto {
    margin: 0 auto !important;
  }
}

.blog-card-two {
  margin-bottom: 30px;
  border-radius: 30px;
  background: var(--white-color);
  box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.07);
}

.blog-card-two .blog-content {
  padding: 36px 40px 40px 40px;
  transition: all ease 0.4s;
  /* Extra small devices */
}

@media (max-width: 416px) {
  .blog-card-two .blog-content {
    padding: 36px 20px 40px;
  }
}

.blog-card-two .blog-meta-two {
  margin-bottom: 10px;
}

.blog-card-two .blog-meta-two span,
.blog-card-two .blog-meta-two a {
  font-family: var(--para-font);
  display: inline-block;
  margin-right: 38px;
  font-size: 16px;
  color: var(--body-color);
}

.blog-card-two .blog-meta-two span:last-child,
.blog-card-two .blog-meta-two a:last-child {
  margin-right: 0;
}

.blog-card-two .blog-meta-two span i,
.blog-card-two .blog-meta-two a i {
  margin-right: 8px;
  color: inherit;
  font-size: 14px;
  color: var(--theme-color);
}

.blog-card-two .blog-meta-two span:hover,
.blog-card-two .blog-meta-two a:hover {
  color: var(--theme-color);
}

.blog-card-two .blog-meta-two span a::before {
  content: ",";
  margin-right: 5px;
  color: var(--body-color) !important;
}

.blog-card-two .blog-meta-two span a:first-of-type::before {
  content: "";
}

.blog-card-two .blog-title {
  font-size: 30px;
  line-height: 40px;
  margin-bottom: 22px;
  font-weight: 700;
  /* Extra small devices */
}

@media (max-width: 416px) {
  .blog-card-two .blog-title {
    font-size: 25px;
    line-height: 30px;
  }
}

.blog-card-two p {
  padding-bottom: 21px;
  margin-bottom: 30px;
  border-bottom: 1px solid #F1F1F1;
}

.blog-card-two .link-btn-two {
  position: relative;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  display: inline-block;
  color: var(--white-color);
  padding: 11px 30px;
  border-radius: 50px;
  background-color: var(--theme-color);
}

.blog-card-two .blog-img {
  border-radius: 30px 30px 0 0;
}

.blog-card-two .blog-img img {
  border-radius: 30px 30px 0 0;
}

.blog-card-two:hover .blog-img img {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .blog-card-two:hover .blog-img img {
    transform: scale(1);
  }
}

.blog-card-three {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  border-radius: 30px;
  background: var(--white-color);
  box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.07);
  padding: 30px 30px 21px;
  /* Medium Large devices */
  /* Small devices */
}

.blog-card-three:last-child {
  margin-bottom: 0;
}

@media (max-width: 1399px) {
  .blog-card-three {
    padding: 20px;
  }
}

@media (max-width: 767px) {
  .blog-card-three {
    flex-direction: column;
    align-items: baseline;
  }
}

.blog-card-three .blog-content {
  border: 1px solid transparent;
  transition: all ease 0.4s;
}

.blog-card-three .blog-img {
  border-radius: 30px;
  overflow: hidden;
  margin-right: 30px;
  /* Small devices */
}

@media (max-width: 767px) {
  .blog-card-three .blog-img {
    width: 100%;
    margin-bottom: 20px;
  }
}

.blog-card-three .blog-img img {
  border-radius: 30px;
  width: 267px;
  /* Small devices */
}

@media (max-width: 767px) {
  .blog-card-three .blog-img img {
    width: 100%;
  }
}

.blog-card-three .blog-meta-two {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  /* Extra small devices */
}

@media (max-width: 416px) {
  .blog-card-three .blog-meta-two {
    flex-direction: column;
    align-items: baseline;
  }
}

.blog-card-three .blog-meta-two span,
.blog-card-three .blog-meta-two a {
  font-family: var(--para-font);
  display: inline-block;
  margin-right: 31px;
  font-size: 16px;
  color: var(--body-color);
  /* Medium Large devices */
}

@media (max-width: 1399px) {

  .blog-card-three .blog-meta-two span,
  .blog-card-three .blog-meta-two a {
    margin-right: 10px;
  }
}

.blog-card-three .blog-meta-two span:last-child,
.blog-card-three .blog-meta-two a:last-child {
  margin-right: 0;
}

.blog-card-three .blog-meta-two span i,
.blog-card-three .blog-meta-two a i {
  margin-right: 8px;
  color: inherit;
  font-size: 14px;
  color: var(--theme-color);
}

.blog-card-three .blog-meta-two span:hover,
.blog-card-three .blog-meta-two a:hover {
  color: var(--theme-color);
}

.blog-card-three .blog-meta-two span a::before {
  content: ",";
  margin-right: 5px;
  color: var(--body-color) !important;
}

.blog-card-three .blog-meta-two span a:first-of-type::before {
  content: "";
}

.blog-card-three .blog-title {
  font-size: 24px;
  line-height: 34px;
  margin-bottom: 25px;
  font-weight: 700;
  padding-bottom: 14px;
  border-bottom: 1px solid #F1F1F1;
}

.blog-card-three p {
  padding-bottom: 21px;
  margin-bottom: 30px;
  border-bottom: 1px solid #F1F1F1;
}

.blog-card-three .link-btn-two {
  position: relative;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  display: inline-block;
  color: var(--white-color);
  padding: 6px 30px;
  border-radius: 50px;
  background-color: var(--theme-color);
}

.blog-card-three:hover .blog-img img {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .blog-card-three:hover .blog-img img {
    transform: scale(1);
  }
}

.vs-blog .blog-audio {
  line-height: 1px;
}

.blog-img {
  border-radius: 50px;
  overflow: hidden;
  display: block;
}

.blog-img img {
  transition: 0.4s ease-in-out;
  border-radius: 50px;
  width: 100%;
}

.block-quote {
  font-size: 22px;
  font-family: var(--para-font);
  line-height: 36px;
  padding: 60px 70px 60px 70px;
  font-weight: 700;
  display: block;
  position: relative;
  background-color: var(--theme-color);
  overflow: hidden;
  margin: 35px 0;
  color: var(--white-color);
  border-radius: 30px;
}

.block-quote p {
  font-size: inherit;
  font-family: inherit;
  margin-bottom: 0 !important;
  line-height: inherit;
  color: inherit;
  width: 100%;
  position: relative;
  z-index: 3;
}

.block-quote:before {
  content: "\f10e";
  font-family: var(--icon-font);
  position: absolute;
  right: 55px;
  bottom: 50px;
  font-size: 11rem;
  font-weight: 300;
  opacity: 1;
  line-height: 7rem;
  color: rgba(255, 255, 255, 0.2);
}

.block-quote p {
  margin-bottom: 0;
}

.block-quote p a {
  color: inherit;
}

.block-quote cite {
  display: inline-block;
  font-size: 18px;
  position: relative;
  border-color: inherit;
  line-height: 1;
  font-weight: 400;
  margin-top: 25px;
  padding-left: 15px;
  font-style: normal;
  font-family: var(--body-font);
  padding-left: 30px;
  margin-left: 10px;
}

.block-quote cite:before {
  content: "";
  height: 3px;
  width: 30px;
  background-color: var(--white-color);
  position: absolute;
  top: 7px;
  left: -10px;
}

.block-quote.style-left-icon {
  font-size: 18px;
  color: var(--body-color);
  font-weight: 400;
  line-height: 1.556;
  background-color: var(--smoke-color);
  border-radius: 30px;
  padding: 55px 60px;
  padding-left: 160px;
}

.block-quote.style-left-icon:before {
  right: unset;
  left: 56px;
  top: 60px;
  font-size: 6rem;
  font-weight: 400;
  line-height: 4rem;
  color: var(--theme-color);
  text-shadow: none;
}

.block-quote.style-left-icon cite {
  color: var(--title-color);
}

.block-quote.style-left-icon cite:before {
  background-color: var(--title-color);
  top: 8px;
}

.blog-meta span,
.blog-meta a {
  font-family: var(--para-font);
  display: inline-block;
  margin-right: 18px;
  font-size: 16px;
  color: var(--body-color);
}

.blog-meta span:last-child,
.blog-meta a:last-child {
  margin-right: 0;
}

.blog-meta span i,
.blog-meta a i {
  margin-right: 8px;
  color: inherit;
  font-size: 14px;
  color: var(--theme-color);
}

.blog-meta span:hover,
.blog-meta a:hover {
  color: var(--theme-color);
}

.blog-meta span a::before {
  content: ",";
  margin-right: 5px;
  color: var(--body-color) !important;
}

.blog-meta span a:first-of-type::before {
  content: "";
}

.blog-category {
  margin-bottom: -10px;
}

.blog-category a {
  display: inline-block;
  color: #fff;
  padding: 4.5px 24.5px;
  margin-right: 5px;
  margin-bottom: 10px;
  border: 1px solid transparent;
  background-color: var(--theme-color);
}

.blog-category a:hover {
  background-color: var(--white-color);
  color: var(--body-color);
  border-color: var(--theme-color);
}

.blog-title a {
  color: inherit;
}

.blog-title a:hover {
  color: var(--theme-color);
}

.blog-inner-title {
  margin-bottom: 30px;
  margin-top: -0.25em;
}

.blog-author {
  margin-top: 60px;
  margin-bottom: 55px;
  padding: 60px;
  border-right: 8px solid var(--theme-color);
  position: relative;
  border-radius: 30px;
  background-color: var(--smoke-color);
}

.blog-author .author-degi {
  margin-bottom: 5px;
  color: var(--theme-color);
  font-weight: 400;
  font-size: 16px;
}

.blog-author .media-img {
  display: inline-block;
  background-color: #e4e4e4;
  margin-right: 25px;
  overflow: hidden;
  border-radius: 30px;
  width: 150px;
  height: 150px;
}

.blog-author .media-img img {
  border-radius: 30px;
}

.blog-author .author-name {
  margin-bottom: 10px;
  color: var(--title-color);
}

.blog-author .author-name a {
  color: inherit;
}

.blog-author .author-name a:hover {
  color: var(--theme-color);
}

.blog-single {
  margin-bottom: 40px;
  border: 2px solid rgb(242, 242, 242);
  border-radius: 30px;
}

.blog-single .blog-meta span,
.blog-single .blog-meta a {
  font-size: 16px;
}

.blog-single .blog-img,
.blog-single .blog-audio {
  overflow: hidden;
  position: relative;
  margin: -2px;
  border-radius: 50px;
}

.blog-single .blog-img img,
.blog-single .blog-audio img {
  border-radius: 50px;
}

.blog-single .blog-audio {
  line-height: 1;
}

.blog-single .blog-content {
  padding: 55px 60px;
  border-top: none;
}

.blog-single .link-btn {
  font-weight: 600;
}

.blog-single .link-btn:before {
  height: 2px;
}

.blog-single .blog-title {
  line-height: 1.3;
  color: var(--title-color);
}

.blog-single .blog-title a {
  color: inherit;
}

.blog-single .blog-title a:hover {
  color: var(--theme-color);
}

.share-links {
  margin-top: 50px;
  padding-bottom: 40px;
  border-bottom: 1px solid var(--border-color);
}

.share-links-title {
  font-size: 18px;
  color: var(--title-color);
  display: block;
  margin-bottom: 10px;
  font-family: var(--title-font);
  font-weight: 600;
  margin-top: -0.45em;
}

.social-links {
  margin-top: -0.05em;
}

.blog-single .blog-meta {
  margin-bottom: 12px;
}

.blog-single .blog-img {
  position: relative;
}

.blog-single .blog-img .slick-arrow {
  position: absolute;
  left: 40px;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
  --title-color: #fff;
}

.blog-single .blog-img .slick-arrow.slick-next {
  left: auto;
  right: 40px;
}

.blog-single .blog-img .play-btn {
  --icon-size: 70px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: calc(var(--icon-size) / -2) 0 0 calc(var(--icon-size) / -2);
}

.blog-single .blog-title {
  margin-bottom: 10px;
}

.blog-single:hover .blog-img .slick-arrow {
  opacity: 1;
  visibility: visible;
}

.blog-details .blog-single {
  position: relative;
  margin-bottom: 0;
  border: none;
}

.blog-details .blog-img,
.blog-details .blog-audio {
  margin: 0;
  border-radius: 50px;
  overflow: hidden;
  margin-bottom: 30px;
}

.blog-details .blog-img img,
.blog-details .blog-audio img {
  border-radius: 50px;
}

.blog-details .blog-title {
  line-height: 1.2;
  font-size: 40px;
  margin-bottom: 15px;
}

.blog-details .blog-meta {
  margin-bottom: 8px;
  margin-left: 3px;
}

.blog-details .blog-meta>span,
.blog-details .blog-meta>a {
  margin-right: 30px;
  font-size: 16px;
}

.blog-details .blog-meta>span:last-child,
.blog-details .blog-meta>a:last-child {
  margin-right: 0;
}

.blog-details .blog-meta>span i,
.blog-details .blog-meta>a i {
  margin-right: 10px;
}

.blog-details .blog-meta span a {
  margin-right: 0;
  font-size: inherit;
}

.blog-details .blog-meta span:hover {
  color: var(--body-color);
}

.blog-details .blog-meta a:hover {
  color: var(--theme-color);
}

.blog-details .blog-content {
  padding: 0;
  border-bottom: none;
  margin-bottom: calc(var(--blog-space-y, 60px) - 10px);
  border: none;
  border-radius: 0;
}

.blog-details .multi-social {
  --icon-size: 35px;
  margin-bottom: 0;
  padding-left: 0;
}

.blog-details .multi-social li {
  display: inline-block;
}

.blog-details .multi-social a {
  margin-left: 5px;
  font-size: 14px;
}

.blog-inner-list ul {
  list-style: none;
  padding-left: 0;
}

.blog-inner-list ul li {
  position: relative;
  padding-left: 40px;
  margin-bottom: 25px;
  font-size: 18px;
  color: var(--title-color);
}

.blog-inner-list ul li:before {
  content: "\f00c";
  font-family: var(--icon-font);
  position: absolute;
  top: 4px;
  left: 0;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  background-color: var(--theme-color);
  color: var(--white-color);
  border-radius: 50%;
}

.blog-inner-list ul li:last-child {
  margin-bottom: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .blog-card {
    --gap: 25px;
  }

  .blog-card .blog-content {
    padding: 135px 25px 23px 25px;
    border-radius: 30px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .blog-img {
    border-radius: 30px;
  }

  .blog-single .blog-content {
    padding: 45px 50px;
  }

  .blog-author {
    padding: 30px;
  }

  .block-quote {
    padding: 30px 40px;
  }

  .block-quote.style-left-icon {
    padding: 30px 40px;
    padding-left: 135px;
  }

  .block-quote.style-left-icon:before {
    left: 36px;
    top: 35px;
  }

  .blog-details .blog-single .blog-title {
    font-size: 32px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .block-quote {
    font-size: 20px;
    line-height: 34px;
  }

  .block-quote cite {
    font-size: 16px;
    margin-top: 20px;
  }

  .block-quote cite:before {
    height: 2px;
  }

  .block-quote:before {
    right: 23px;
    bottom: 20px;
    font-size: 8rem;
    line-height: 5rem;
    text-shadow: 3px 0 0;
  }

  .block-quote.style-left-icon {
    padding-left: 40px;
  }

  .block-quote.style-left-icon:before {
    left: unset;
    top: unset;
    right: 30px;
    bottom: 30px;
  }

  .blog-author {
    padding: 30px 30px;
  }

  .blog-author .media-img {
    width: 100px;
    height: 100px;
  }

  .blog-single .blog-content {
    padding: 55px 60px;
  }

  .blog-single .blog-title {
    line-height: 1.4;
    font-size: 30px;
  }

  .blog-single .mb-50,
  .blog-single .mb-40 {
    margin-bottom: 30px;
  }

  .vs-pagination.mt-60 {
    margin-top: 30px;
  }

  .blog-details .blog-single .blog-title {
    font-size: 28px;
  }
}

/* Small devices */
@media (max-width: 767px) {

  .blog-meta span,
  .blog-meta a {
    margin-right: 20px;
    font-size: 16px;
  }

  .share-links .row {
    gap: 30px 0;
  }

  .blog-author {
    padding: 25px;
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .block-quote {
    padding: 25px;
    font-size: 16px;
    line-height: 26px;
    border-radius: 15px;
  }

  .block-quote:before {
    right: 22px;
    font-size: 5rem;
    line-height: 3rem;
    text-shadow: 2px 0 0;
  }

  .block-quote.style-left-icon {
    padding: 25px;
    padding-left: 25px;
    border-radius: 15px;
  }

  .block-quote.style-left-icon:before {
    right: 25px;
    bottom: 17px;
    font-size: 4rem;
  }

  .blog-single .blog-content {
    padding: 35px 40px;
  }

  .blog-single .share-links-title {
    margin-bottom: 6px;
  }

  .blog-details .blog-single {
    position: relative;
  }

  .blog-details .blog-single .blog-title {
    line-height: 1.2;
    font-size: 26px;
  }

  .blog-details .blog-single .blog-meta li {
    font-size: 14px;
    margin-right: 20px;
  }

  .blog-details .blog-content {
    margin-bottom: 0;
  }

  .blog-details .multi-social {
    --icon-size: 35px;
  }

  .blog-details .multi-social a {
    margin-left: 0;
    margin-right: 5px;
  }

  .blog-author .media-img {
    width: 100px;
    height: 100px;
  }

  .share-links {
    margin-top: 20px;
    padding-bottom: 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .blog-single .blog-content {
    padding: 25px 20px;
  }

  .blog-single .blog-meta.mb-15 {
    margin-bottom: 5px;
  }

  .blog-inner-list ul {
    padding-left: 0;
  }

  .blog-single .blog-title {
    font-size: 20px;
  }

  .vs-comment-form {
    padding: 25px 20px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .blog-card {
    --gap: 25px;
  }

  .blog-card .blog-img {
    border-radius: 30px;
  }

  .blog-card .blog-content {
    border-radius: 30px;
    padding: 130px 25px 33px 25px;
  }
}

/*------------------- 4.7. Comments  -------------------*/
.vs-comment-wrap .blog-inner-title {
  margin-bottom: 0;
}

ul.comment-list,
.children {
  list-style: none;
  margin: 0;
  padding: 0;
}

.vs-post-comment {
  --border-color: rgb(241, 240, 239);
  padding: 40px 0 34px 0;
  transition: 0.4s;
  border-bottom: 1px solid var(--border-color);
}

.vs-post-comment .comment-avater {
  width: 100px;
  height: 100px;
  margin-right: 30px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
}

.vs-post-comment .comment-avater img {
  border-radius: 50%;
}

.vs-post-comment .comment-content {
  position: relative;
  line-height: 1;
  padding-left: 130px;
}

.vs-post-comment .name {
  margin-bottom: 15px;
  margin-top: -5px;
  font-size: 20px;
  font-weight: 600;
}

.vs-post-comment .commented-on {
  font-size: 14px;
  display: block;
  font-weight: 400;
  margin-bottom: 10px;
  font-family: var(--para-font);
  color: var(--theme-color);
  text-transform: uppercase;
}

.vs-post-comment .reply_and_edit,
.vs-post-comment .star-rating {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
}

.vs-post-comment .star-rating {
  width: 92px;
}

.vs-post-comment .reply-btn {
  color: var(--theme-color);
  min-width: 112px;
  font-family: var(--body-font);
  padding: 0 20px;
  height: 40px;
  display: inline-block;
  text-align: center;
  line-height: 40px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
}

.vs-post-comment .reply-btn:before {
  content: "\f3e5";
  font-family: var(--icon-font);
  font-weight: 600;
  margin-right: 5px;
}

.vs-post-comment .reply-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.vs-post-comment .text {
  margin-bottom: 0;
}

.children .vs-post-comment {
  padding-left: 70px;
}

ul.comment-list .vs-comment:first-child:not(.children .vs-comment)>.vs-post-comment {
  padding-top: 30px;
}

ul.comment-list .vs-comment:last-child:not(.children .vs-comment)>.vs-post-comment {
  border-bottom: none;
}

.vs-comment-form,
.vs-register-form {
  background-color: var(--white-color);
  border: 2px solid rgb(242, 242, 242);
  padding: 60px;
  margin-top: 15px;
  border-radius: 30px;
}

.vs-comment-form .row,
.vs-register-form .row {
  --bs-gutter-x: 20px;
}

.vs-comment-form .form-group:last-child,
.vs-register-form .form-group:last-child {
  margin-top: 10px;
}

.vs-comment-form .blog-inner-title,
.vs-register-form .blog-inner-title {
  margin-bottom: 0;
}

.vs-comment-form .form-text,
.vs-register-form .form-text {
  margin-bottom: 25px;
}

.form-text {
  font-size: 1em;
}

.vs-register-form {
  margin-top: 40px;
}

.vs-register-form .form-title {
  margin-bottom: 30px;
}

.vs-comment-form input,
.vs-comment-form select {
  height: 60px;
}

.vs-comment-form select.style2~i,
.vs-comment-form .form-select.style2~i,
.vs-comment-form .form-control.style2~i {
  top: 17.5px;
  font-size: 16px;
}

/* Large devices */
@media (max-width: 1199px) {

  .vs-comment-form,
  .vs-register-form {
    padding: 30px;
  }
}

/* Medium devices */
@media (max-width: 991px) {

  .vs-comment-form,
  .vs-register-form {
    margin-bottom: 30px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .vs-post-comment {
    padding: 25px 0 20px 0;
  }

  .vs-post-comment .comment-avater {
    float: none;
  }

  .vs-post-comment .comment-content {
    padding-left: 0;
    margin-top: 25px;
  }

  .children .vs-post-comment {
    padding-left: 50px;
  }

  .vs-comment-form,
  .vs-register-form {
    margin-top: 20px;
    padding: 25px;
  }

  .vs-comment-form .mb-40,
  .vs-register-form .mb-40 {
    margin-bottom: 20px;
  }

  .form-title .form-text {
    font-size: 14px;
    line-height: 20px;
    margin-top: 10px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .vs-post-comment {
    flex-direction: column;
  }

  .vs-post-comment .comment-avater {
    margin-right: 20px;
    margin-bottom: 20px;
  }

  .vs-post-comment .comment-content {
    width: 100%;
  }
}

/*------------------- 4.8. Hero Area  -------------------*/
.vs-hero-wrapper .section-before,
.breadcrumb-section .section-before {
  top: -5px;
}

.hero-slider1 {
  position: relative;
}

.hero-slider1 .vs-hero-inner {
  padding: 100px 0 140px 0;
}

.vs-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.vs-hero-bg img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  /*
  opacity: 0.55;
  */
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
}

.hero-content {
  max-width: 660px;
  padding: 150px;
  right: 180px;
  margin-left: auto;
  text-align: center;
  position: relative;
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;
}

.hero-content .tr-element {
  background-color: var(--white-color);
  opacity: 0.9;
}

.hero-content img.car {
  position: absolute;
  top: 17px;
  left: 63px;
}

.con-before {
  position: absolute;
  left: -26px;
  top: 19%;
  transform: rotate(3deg);
}

.con-after {
  position: absolute;
  right: -26px;
  top: 20%;
  transform: rotate(3deg);
}

.hero-title .focus {
  font-size: 90px;
  margin-bottom: -4px;
  line-height: 1.1;
  display: block;
}

.hero-title.style-2 {
  margin-bottom: 35px;
  font-size: 72px;
  color: var(--title-color);
}

.hero-subtitle.style-2 {
  font-weight: 500;
  display: block;
  margin-bottom: 35px;
  color: var(--title-color);
}

.hero-text {
  margin-bottom: 40px;
  color: var(--blue-color);
}

.hero-text span {
  font-weight: 600;
  font-size: 24px;
  position: relative;
}

.hero-text i {
  font-size: 8px;
  margin: 0 20px;
  position: relative;
  top: -4px;
}

.hero-2 {
  position: relative;
  overflow: hidden;
}

.hero-2 .section-after {
  margin-bottom: -35px;
}

.hero-2 .shape-mockup {
  position: absolute !important;
}

.hero-2 .vs-hero-inner {
  text-align: center;
  position: relative;
  z-index: 2;
  height: 840px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.hero-img-1 {
  position: absolute !important;
  top: 50px;
  left: 45px;
  z-index: 1;
}

.hero-img-2 {
  position: absolute !important;
  bottom: 80px;
  right: 60px;
  z-index: 1;
}

.hero-slider3 .slick-arrow {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white-color);
  border: none;
  top: 50%;
  left: 80px;
  transform: translateY(-50%);
  width: var(--icon-size, 60px);
  height: var(--icon-size, 60px);
  z-index: 2;
}

.hero-slider3 .slick-arrow.slick-next {
  right: 80px;
  left: unset;
}

.hero-slider3 .slick-arrow:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.hero-3 .vs-hero-inner {
  padding: 100px 0;
  height: 815px;
  display: flex;
  align-items: center;
}

.hero-content3 {
  max-width: 760px;
  text-align: center;
  position: relative;
  z-index: 3;
  margin: 0 auto;
}

.hero-content3 .hero-title {
  color: var(--white-color);
}

.hero-subtitle.style-3 {
  font-size: 20px;
  font-weight: 500;
  display: block;
  margin-bottom: 15px;
  color: var(--white-color);
}

/* Hight Resoulation devices */
@media (min-width: 1922px) {
  div#cloud {
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

/* Extra large devices */
@media (max-width: 1680px) {
  .hero-img-2 {
    bottom: 10px;
    right: 15px;
  }

  .hero-3 .vs-hero-inner {
    height: 600px;
  }
}

/* Medium Large devices */
@media (max-width: 1399px) {

  .vs-hero-wrapper .section-before,
  .breadcrumb-section .section-before {
    top: -8px;
  }

  .hero-content {
    right: 50px;
    margin-left: auto;
    text-align: center;
    max-width: 580px;
    padding: 130px 100px;
  }

  .hero-title .focus {
    font-size: 70px;
  }

  .hero-title.style-2 {
    font-size: 60px;
  }

  .hero-img-1 {
    left: 10px;
  }

  .hero-img-2 {
    max-width: 400px !important;
  }

  .hero-3 .vs-hero-inner {
    height: 540px;
  }
}

/* Large devices */
@media (max-width: 1199px) {

  .vs-hero-wrapper .shape-before img,
  .vs-hero-wrapper .section-before img {
    height: 20px;
  }

  .hero-2 .shape-after img,
  .hero-2 .section-after img {
    height: 80px;
  }

  .hero-3 .shape-after img,
  .hero-3 .section-after img {
    height: 45px;
  }

  .hero-slider1 .vs-hero-inner {
    padding: 100px 0 100px 0;
  }

  .hero-content {
    right: unset;
    margin-right: auto;
    max-width: 560px;
  }

  .hero-2 .vs-hero-inner {
    height: 520px;
  }

  .hero-2 #fire {
    max-width: 140px;
  }

  .hero-2 #rocket {
    bottom: 6% !important;
    left: 27% !important;
  }

  .hero-2 .section-after {
    margin-bottom: -20px;
  }

  .hero-title.style-2 {
    font-size: 48px;
  }

  .hero-img-2 {
    bottom: 0;
  }

  .hero-img-1 {
    max-width: 300px !important;
  }

  .hero-img-2 {
    max-width: 320px !important;
  }

  .hero-3 .vs-hero-inner {
    height: 600px;
  }
}

/* Medium devices */
@media (max-width: 991px) {

  .vs-hero-wrapper .shape-before img,
  .vs-hero-wrapper .section-before img {
    height: 15px;
  }

  .hero-title .focus {
    font-size: 50px;
  }

  .hero-subtitle.style-2.mb-35 {
    margin-bottom: 10px !important;
  }

  .hero-img-1 {
    display: none !important;
  }

  .hero-img-2 {
    display: none !important;
  }

  .hero-2 #rocket {
    bottom: 5% !important;
    left: 10% !important;
  }

  .hero-2 .vs-hero-inner {
    padding: 120px 0 120px 0;
    height: auto;
  }

  .hero-3 .vs-hero-inner {
    height: auto;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .hero-slider1 .vs-hero-inner {
    padding: 80px 50px 60px 50px;
  }

  .hero-content {
    background-size: contain;
  }

  .hero-content img.car {
    top: -25px;
  }

  .con-before {
    display: none !important;
  }

  .con-after {
    display: none !important;
  }

  .hero-content {
    padding: 100px 20px 60px;
  }

  .hero-text {
    margin-bottom: 20px;
  }

  .hero-title {
    font-size: 32px;
    line-height: 1.6;
  }

  .hero-title .focus {
    font-size: 44px;
  }

  .hero-title.style-2 {
    font-size: 36px;
    line-height: 1.2;
    margin-bottom: 20px;
  }

  .hero-subtitle.style-2 {
    margin-bottom: 20px;
  }

  .hero-2 #rocket {
    max-width: 55px;
    bottom: 5% !important;
    left: 10% !important;
  }

  .hero-2 #fire {
    max-width: 100px;
    left: -10% !important;
  }

  .hero-2 #star {
    max-width: 40px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .hero-img-2 {
    display: none !important;
  }

  .hero-slider1 .vs-hero-inner {
    padding: 80px 25px 60px 25px;
  }

  .hero-content {
    padding: 60px 20px 40px;
  }

  .hero-content img.car {
    top: 68px;
    max-width: 90px;
    left: -10px;
  }

  .hero-text {
    margin-bottom: 8px;
  }

  .hero-title.mb-30 {
    margin-bottom: 15px;
  }
}

@media (max-width: 350px) {
  .hero-text {
    margin-bottom: 0;
  }

  .hero-text span {
    font-size: 20px;
  }

  .hero-title {
    font-size: 26px;
    line-height: 1.4;
  }

  .hero-title .focus {
    font-size: 36px;
    line-height: 1;
  }

  .hero-content img.car {
    max-width: 60px;
  }
}

.vs-hero-wrapper-four {
  padding: 180px 0 30px;
  margin-top: -170px;
  z-index: 3;
  /* Large devices */
  /* Small devices */
  /* Extra small devices */
}

@media (max-width: 1199px) {
  .vs-hero-wrapper-four {
    padding: 190px 0 50px;
  }
}

@media (max-width: 767px) {
  .vs-hero-wrapper-four {
    padding: 50px 0;
    margin-top: 0;
  }
}

@media (max-width: 416px) {
  .vs-hero-wrapper-four {
    padding: 40px 25px 40px 25px;
  }
}

.vs-hero-wrapper-four .slideinup {
  opacity: 1;
}

.vs-hero-wrapper-four .hero-content4 {
  position: relative;
  z-index: 3;
  max-width: 699px;
}

.vs-hero-wrapper-four .hero-content4 .hero-title {
  color: var(--title-color);
  font-size: 72px;
  font-weight: 700;
  line-height: 82px;
  margin-bottom: 39px;
  /* Small devices */
  /* Small devices */
  /* Extra small devices */
}

@media (max-width: 767px) {
  .vs-hero-wrapper-four .hero-content4 .hero-title {
    font-size: 65px;
    line-height: 68px;
  }
}

@media (max-width: 767px) {
  .vs-hero-wrapper-four .hero-content4 .hero-title {
    font-size: 56px;
    line-height: 59px;
  }
}

@media (max-width: 416px) {
  .vs-hero-wrapper-four .hero-content4 .hero-title {
    font-size: 38px;
    line-height: 45px;
    margin-bottom: 25px;
  }
}

.vs-hero-wrapper-four .hero-content4 .hero-subtitle {
  font-size: 24px;
  font-weight: 500;
  display: block;
  color: var(--title-color);
  margin-bottom: 32px;
  /* Extra small devices */
}

@media (max-width: 416px) {
  .vs-hero-wrapper-four .hero-content4 .hero-subtitle {
    font-size: 22px;
    margin-bottom: 15px;
  }
}

/*------------------- 4.9. Error  -------------------*/
.vs-error-wrapper {
  position: relative;
  min-height: 100vh;
  background-color: var(--title-color);
}

.error-shape {
  position: absolute !important;
  top: 12%;
  right: 15%;
  max-width: 630px;
}

.error-content {
  max-width: 570px;
  padding-top: 254px;
  padding-bottom: 254px;
}

.error-title {
  font-size: 80px;
  margin-bottom: 10px;
}

.error-text {
  font-size: 24px;
  margin-bottom: 48px;
}

.error-number {
  color: var(--white-color);
  display: block;
  margin-bottom: 0;
  line-height: 1;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .error-content {
    max-width: 480px;
  }

  .error-title {
    font-size: 64px;
  }

  .error-shape {
    max-width: 520px;
  }

  .error-text {
    font-size: 20px;
    margin-bottom: 30px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .vs-error-wrapper {
    position: relative;
    min-height: 500px;
  }

  .error-shape {
    max-width: 440px;
    top: 20%;
    right: 5%;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .error-shape {
    display: none !important;
  }

  .error-content {
    text-align: center;
    max-width: 460px;
    margin-left: auto;
    margin-right: auto;
  }

  .error-title {
    font-size: 48px;
  }

  .error-text {
    font-size: 18px;
  }

  .error-shape {
    display: none;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .error-title {
    font-size: 36px;
  }

  .error-text {
    font-size: 16px;
  }
}

/*------------------- 4.10. Popup Search  -------------------*/
.popup-search-box {
  position: fixed;
  top: 0;
  left: 50%;
  background-color: rgba(0, 0, 0, 0.95);
  height: 0;
  width: 0;
  overflow: hidden;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: all ease 0.4s;
}

.popup-search-box button.searchClose {
  width: 60px;
  height: 60px;
  line-height: 60px;
  position: absolute;
  top: 40px;
  right: 40px;
  border-width: 1px;
  border-style: solid;
  border-color: var(--theme-color);
  background-color: transparent;
  font-size: 22px;
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
}

.popup-search-box button.searchClose:hover {
  color: var(--body-color);
  background-color: #fff;
  border-color: transparent;
  border-color: transparent;
  transform: rotate(90deg);
}

.popup-search-box form {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  padding-bottom: 40px;
  cursor: auto;
  width: 100%;
  max-width: 700px;
  transform: translate(-50%, -50%) scale(0);
  transition: transform ease 0.4s;
  /* Large devices */
}

@media (max-width: 1199px) {
  .popup-search-box form {
    max-width: 600px;
  }
}

.popup-search-box form input {
  font-size: 18px;
  height: 70px;
  width: 100%;
  border-width: 2px;
  border-style: solid;
  border-color: var(--theme-color);
  background-color: transparent;
  padding-left: 30px;
  color: #fff;
  border-radius: 50px;
}

.popup-search-box form input::-moz-placeholder {
  color: #fff;
}

.popup-search-box form input::-webkit-input-placeholder {
  color: #fff;
}

.popup-search-box form input:-ms-input-placeholder {
  color: #fff;
}

.popup-search-box form input::placeholder {
  color: #fff;
}

.popup-search-box form button {
  position: absolute;
  top: 0px;
  background-color: transparent;
  border: none;
  color: #fff;
  font-size: 24px;
  right: 12px;
  color: var(--white-color);
  cursor: pointer;
  width: 70px;
  height: 70px;
  transition: all ease 0.4s;
  transform: scale(1.001);
}

.popup-search-box form button:hover {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .popup-search-box form button:hover {
    transform: scale(1);
  }
}

.popup-search-box.show {
  opacity: 1;
  visibility: visible;
  width: 100.1%;
  height: 100%;
  transition: all ease 0.4s;
  border-radius: 0;
}

.popup-search-box.show form {
  transition-delay: 0.5s;
  transform: translate(-50%, -50%) scale(1);
}

/*------------------- 4.11. Popup Side Menu  -------------------*/
.sidemenu-wrapper {
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.8s;
}

.sidemenu-wrapper .closeButton {
  display: inline-block;
  border: none;
  width: 40px;
  height: 40px;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  position: absolute;
  top: 20px;
  right: 20px;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
}

.sidemenu-wrapper .closeButton:hover {
  color: var(--white-color);
  background-color: var(--secondary-color);
  transform: rotate(90deg);
}

.sidemenu-wrapper .sidemenu-content {
  background-color: var(--white-color);
  width: 380px;
  margin-left: auto;
  padding: 50px 40px;
  height: 100%;
  overflow: scroll;
  position: relative;
  right: -500px;
  cursor: auto;
  transition-delay: 1s;
  transition: right ease 1s;
}

.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  background-color: #F5F5F5;
}

.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar {
  width: 2px;
  background-color: #F5F5F5;
}

.sidemenu-wrapper .widget {
  padding: 0;
  border: none;
  background-color: transparent;
}

.sidemenu-wrapper.show {
  opacity: 1;
  visibility: visible;
  width: 100%;
  transition: all ease 0.8s;
}

.sidemenu-wrapper.show .sidemenu-content {
  right: 0;
  opacity: 1;
  visibility: visible;
}

/*------------------- 4.12. Wocommerce  -------------------*/
.woocommerce-message,
.woocommerce-info {
  position: relative;
  border: 1px solid #d8d8d8;
  padding: 11px 20px;
  background-color: var(--body-color);
  color: var(--white-color);
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 30px;
  border-radius: 5px;
}

.woocommerce-message a,
.woocommerce-info a {
  color: inherit;
  text-decoration: underline;
}

.woocommerce-message a:hover,
.woocommerce-info a:hover {
  text-decoration: underline;
}

.woocommerce-message:before,
.woocommerce-info:before {
  content: "\f06a";
  font-family: var(--icon-font);
  font-weight: 900;
  margin-right: 10px;
}

.woocommerce-notices-wrapper .woocommerce-message {
  background-color: var(--theme-color);
}

.woocommerce-notices-wrapper .woocommerce-message:before {
  content: "\f14a";
  font-weight: 300;
}

.woocommerce-form-login-toggle .woocommerce-info {
  background-color: var(--theme-color);
}

.woocommerce-error {
  background-color: var(--error-color);
  color: #fff;
  list-style: none;
  padding: 10px 26px;
  margin: 0 0 30px 0;
  border-radius: 5px;
  font-weight: 700;
  font-size: 14px;
}

nav.woocommerce-MyAccount-navigation li {
  border: 1px solid #ddd;
  margin: 0;
  border-top: none;
}

nav.woocommerce-MyAccount-navigation li:first-child {
  border-top: 1px solid #ddd;
}

nav.woocommerce-MyAccount-navigation li a {
  color: var(--title-color);
  font-weight: 700;
  padding: 7px 17px;
  display: block;
}

nav.woocommerce-MyAccount-navigation li.is-active a,
nav.woocommerce-MyAccount-navigation li a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}

.woocommerce-MyAccount-content h3 {
  margin-top: -0.3em;
}

.woocommerce-MyAccount-content .btn {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 14px;
  padding: 10px 25px;
  font-weight: 700;
}

.woocommerce-MyAccount-content .btn:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

table.variations,
.woocommerce-grouped-product-list-item {
  border-collapse: separate;
  border-spacing: 0 15px;
  margin-bottom: 5px;
  align-items: center;
}

table.variations td,
.woocommerce-grouped-product-list-item td {
  border: none;
  vertical-align: middle;
  padding: 0 5px;
}

table.variations td:first-child,
.woocommerce-grouped-product-list-item td:first-child {
  padding: 0;
}

table.variations label,
.woocommerce-grouped-product-list-item label {
  margin: 0;
  font-size: 14px;
  text-transform: capitalize;
}

table.variations label a,
.woocommerce-grouped-product-list-item label a {
  color: var(--title-color);
}

table.variations label a:hover,
.woocommerce-grouped-product-list-item label a:hover {
  color: var(--theme-color);
}

.woocommerce-product-attributes th,
.woocommerce-product-attributes td {
  border: 1px solid var(--border-color);
}

.woocommerce-product-attributes th p:last-child,
.woocommerce-product-attributes td p:last-child {
  margin-bottom: 0;
}

.woocommerce-grouped-product-list.group_table {
  border-collapse: collapse;
  margin-bottom: 15px;
}

.woocommerce-grouped-product-list.group_table .woocommerce-Price-amount.amount {
  font-size: 16px;
  color: var(--title-color);
}

.woocommerce-grouped-product-list.group_table label {
  margin: 0 0 0 10px;
  margin: 0 0 0 10px;
  font-family: var(--title-font);
  font-size: 18px;
}

.woocommerce-grouped-product-list.group_table .qty-input {
  border-color: #e3e6e9;
}

.woocommerce-grouped-product-list.group_table tr {
  border-bottom: 1px solid #e3e6e9;
}

.woocommerce-grouped-product-list.group_table tr:last-child {
  border-bottom: none;
}

.woocommerce-grouped-product-list.group_table td {
  padding: 30px 5px;
}

table.variations {
  width: max-content;
  position: relative;
}

table.variations td {
  padding: 0;
}

table.variations td.label {
  padding-right: 10px;
  width: max-content;
}

table.variations select {
  width: max-content;
  font-weight: 400;
  line-height: 1.5;
  vertical-align: middle;
  margin: 0;
  padding-right: 54px;
  padding-left: 20px;
  height: 50px;
}

table.variations .reset_variations {
  margin-left: 16px;
  display: inline-block;
  position: absolute;
  left: 100%;
  bottom: 25px;
}

.woocommerce-variation.single_variation {
  margin-bottom: 30px;
}

.woocommerce-variation.single_variation .price {
  color: var(--title-color);
  font-weight: 700;
}

.wooscp-table-items td.woocommerce-product-attributes-item__value {
  padding-left: 15px !important;
}

.wooscp-table-items a.added_to_cart.wc-forward {
  margin-left: 15px;
  text-decoration: underline;
}

#woosq-popup .product_meta {
  margin-top: 20px;
}

#woosq-popup .product_title {
  font-size: 24px;
  margin-bottom: 5px;
}

#woosq-popup .single-product .product .actions {
  align-items: center;
  display: flex;
  gap: 20px;
}

#woosq-popup .single-product .product .actions .vs-btn {
  padding: 17px 30px;
}

#woosq-popup .single-product .product .actions>div {
  height: auto;
  overflow: visible;
  width: max-content;
}

#woosq-popup .single-product .product .actions>div .quantity.style2.woocommerce-grouped-product-list-item__quantity {
  width: max-content;
}

.login-tab {
  margin-bottom: 30px;
  justify-content: center;
}

.login-tab button.nav-link {
  background-color: var(--smoke-color);
  color: var(--title-color);
  padding: 11px 39px;
  font-size: 18px;
  font-weight: 500;
  border-radius: 15px ​15px 0;
}

.login-tab button.nav-link.active {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.star-rating {
  overflow: hidden;
  position: relative;
  width: 100px;
  height: 1.2em;
  line-height: 1.2em;
  display: block;
  font-family: var(--icon-font);
  font-weight: 700;
  font-size: 14px;
}

.star-rating:before {
  content: "\f005\f005\f005\f005\f005";
  color: #e1e1e1;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  letter-spacing: 3px;
}

.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}

.star-rating span:before {
  content: "\f005\f005\f005\f005\f005";
  top: 0;
  position: absolute;
  left: 0;
  color: var(--yellow-color);
  letter-spacing: 3px;
}

.rating-select label {
  margin: 0;
  margin-right: 10px;
}

.rating-select p.stars {
  margin-bottom: 0;
  line-height: 1;
}

.rating-select p.stars a {
  position: relative;
  height: 14px;
  width: 18px;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
}

.rating-select p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 14px;
  line-height: 1;
  font-family: var(--icon-font);
  content: "\f005";
  font-weight: 400;
  text-indent: 0;
  color: var(--yellow-color);
}

.rating-select p.stars a:hover~a::before {
  content: "\f005";
  font-weight: 400;
}

.rating-select p.stars:hover a::before {
  content: "\f005";
  font-weight: 700;
}

.rating-select p.stars.selected a.active::before {
  content: "\f005";
  font-weight: 700;
}

.rating-select p.stars.selected a.active~a::before {
  content: "\f005";
  font-weight: 400;
}

.rating-select p.stars.selected a:not(.active)::before {
  content: "\f005";
  font-weight: 700;
}

/* Small devices */
@media (max-width: 767px) {

  .woocommerce-message,
  .woocommerce-info {
    font-size: 14px;
    padding: 5px 10px;
  }
}

/*------------------- 4.13. Products  -------------------*/
.vs-product-box {
  text-align: center;
  transition: all ease 0.4s;
}

.vs-product-box .rating-wrap {
  display: inline-block;
  margin-bottom: 8px;
}

.vs-product-box .rating-wrap:empty {
  display: none;
}

.vs-product-box .star-rating {
  margin-right: -0.7em;
}

.vs-product-box .product-title {
  margin-bottom: 8px;
}

.vs-product-box .product-title a {
  color: inherit;
}

.vs-product-box .product-title a:hover {
  color: var(--theme-color);
}

.vs-product-box .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.vs-product-box .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 50px;
  height: 50px;
  line-height: 50px;
  display: inline-block;
}

.vs-product-box .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.vs-product-box .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  position: relative;
  top: 0;
  left: 0;
  line-height: inherit;
  margin: 0;
  font-size: 24px;
}

.vs-product-box .tinv-wishlist a {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background-color: var(--white-color);
  color: var(--title-color);
  border-radius: 50%;
}

.vs-product-box .tinv-wishlist a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.vs-product-box .product-content {
  padding: 25px 20px 10px 20px;
  border-top: none;
  transition: all ease 0.4s;
}

.vs-product-box .product-img {
  background-color: #f3f6f7;
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}

.vs-product-box .product-img img {
  border-radius: 20px;
  transition: 0.5s;
}

.vs-product-box .actions {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  transition: 0.4s ease-in-out;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.7);
  z-index: 2;
}

.vs-product-box .actions .vs-btn {
  height: 50px;
  padding: 10px 20px;
}

.vs-product-box .add_to_cart_button.added {
  display: none;
}

.vs-product-box .added_to_cart {
  width: 50px;
  height: 50px;
  line-height: 50px;
  background-color: var(--white-color);
  color: var(--title-color);
  font-size: 0;
  text-align: center;
  border-radius: 50%;
}

.vs-product-box .added_to_cart:after {
  content: "\f07a";
  position: relative;
  font-family: var(--icon-font);
  font-size: 16px;
  font-weight: 700;
}

.vs-product-box .added_to_cart:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

.vs-product-box:hover .product-content {
  border-color: transparent;
  background-color: var(--white-color);
}

.vs-product-box:hover .product-img img {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .vs-product-box:hover .product-img img {
    transform: scale(1);
  }
}

.vs-product-box:hover .actions {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.vs-product-box .product-tag {
  width: 60px;
  height: 30px;
  font-size: 16px;
  font-weight: 600;
  background-color: var(--theme-color);
  line-height: 31px;
  color: var(--white-color);
  border-radius: 15px;
  position: absolute;
  top: 30px;
  right: 30px;
}

.vs-product-box.list-view {
  display: flex;
  text-align: left;
}

.vs-product-box.list-view .product-img {
  width: 100%;
  max-width: 180px;
}

.vs-product-box.list-view .star-rating {
  font-size: 12px;
  width: 86px;
}

.vs-product-box.list-view .product-content {
  flex: 1;
  border-top: 1px solid #f3f6f7;
  border-left: none;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.vs-product-box.list-view .icon-btn {
  width: auto;
  height: auto;
  line-height: auto;
  margin: 0 3px;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 13px;
}

.vs-product-box.list-view .added_to_cart,
.vs-product-box.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.vs-product-box.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 35px;
  height: 35px;
  line-height: 35px;
}

.vs-product-box.list-view .added_to_cart:before {
  font-size: 14px;
}

.vs-product-box.list-view .added_to_cart:after {
  font-size: 13px;
}

.vs-product-box.list-view .tinv-wraper.tinv-wishlist {
  line-height: 1;
}

.vs-product-box.list-view .icon-btn {
  width: auto;
  height: auto;
  line-height: auto;
  margin: 0 3px;
}

.vs-product-box.list-view .icon-btn i {
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 13px;
}

.price,
.amount {
  color: var(--theme-color);
  font-weight: 700;
  font-size: 18px;
  font-family: var(--body-font);
}

.price del,
.amount del {
  color: #c1c9cb;
  margin-right: 12px;
}

del {
  color: #c1c9cb;
}

del .price,
del .amount {
  color: #c1c9cb;
}

.vs-sort-bar .icon-btn {
  margin-right: 10px;
}

.vs-sort-bar .icon-btn:last-child {
  margin-right: 0;
}

.vs-sort-bar select {
  height: 60px;
  border: 1px solid #f2eeed;
  width: fit-content;
  min-width: auto;
  color: var(--body-color);
  padding-right: 46px;
  padding-left: 20px;
  font-size: 16px;
  margin: 0;
  border-radius: 30px;
}

.vs-sort-bar label {
  font-size: 14px;
  margin-bottom: 0;
  margin-right: 15px;
  font-weight: 500;
  line-height: 1;
  color: var(--title-color);
}

.vs-sort-bar p.woocommerce-result-count {
  margin-bottom: 0;
  color: var(--title-color);
}

.product-search .widget {
  padding: 0;
  margin: 0;
}

.product-big-img .slick-arrow,
.product-thumb-img .slick-arrow {
  position: absolute;
  left: -60px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background-color: transparent;
  width: auto;
  height: auto;
  padding: 0;
  line-height: 1;
  font-size: 24px;
  color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  z-index: 3;
  margin: 0;
}

.product-big-img .slick-arrow.slick-next,
.product-thumb-img .slick-arrow.slick-next {
  left: auto;
  right: -60px;
}

.product-big-img .slick-dots {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  bottom: 25px;
  margin-bottom: 0;
}

.product-big-img .slick-arrow {
  left: 30px;
}

.product-big-img .slick-arrow.slick-next {
  left: auto;
  right: 30px;
}

.product-thumb-img .thumb {
  width: 150px;
  max-width: 100%;
  border: 1px solid #e4e4e4;
  transition: all ease 0.4s;
  cursor: pointer;
}

.product-thumb-img .slick-current .thumb {
  border-color: var(--theme-color);
}

.product-thumb-img .slick-arrow {
  position: absolute;
  left: -60px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background-color: transparent;
  width: auto;
  height: auto;
  padding: 0;
  line-height: 1;
  font-size: 24px;
  color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
}

.product-thumb-img .slick-arrow.slick-next {
  left: auto;
  right: -60px;
}

.product-thumb-img .slick-dots {
  margin-top: 20px;
}

.container .row:hover .product-big-img .slick-arrow,
.container .row:hover .product-thumb-img .slick-arrow {
  opacity: 1;
  visibility: visible;
}

.quantity {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #eeeeee;
  border-radius: 25px;
  text-align: center;
  width: 150px;
}

.quantity input {
  width: 50px;
  height: 48px;
  text-align: center;
  border: 1px solid #eeeeee;
  border-top: none;
  border-bottom: none;
  font-family: var(--para-font);
  color: var(--body-color);
  font-weight: 400;
  font-size: 16px;
  padding-left: 0;
  /* Firefox */
}

.quantity input::-webkit-outer-spin-button,
.quantity input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity input[type=number] {
  -moz-appearance: textfield;
}

.quantity .qut-btn {
  border: none;
  background-color: transparent;
  padding: 0;
  line-height: 1;
  color: var(--body-color);
  font-size: 14px;
  text-align: center;
  width: 50px;
}

.quantity .qut-btn:hover {
  color: var(--theme-color);
}

.product-inner-list>ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.product-inner-list li {
  position: relative;
  padding-left: 15px;
}

.product-inner-list li:before {
  content: "-";
  position: absolute;
  left: 0;
}

.product-about>.price {
  margin-top: -0.2em;
  font-size: 30px;
  font-weight: 600;
  color: var(--theme-color);
  display: block;
  margin-bottom: 15px;
}

.product-about>.price del {
  color: var(--body-color);
  font-weight: 400;
  font-size: 0.8em;
}

.product-about .actions-btn {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.product-about .actions-btn .vs-btn {
  font-size: 16px;
  padding: 8px 28px;
  height: 50px;
}

.product-about .vs-comments-wrap {
  margin-top: 0;
}

.product-about .border-title {
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 40px;
}

.product-about .border-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 80px;
  background-color: var(--theme-color);
}

/* Extra small devices */
@media (max-width: 575px) {
  .vs-sort-bar {
    text-align: center;
  }

  .vs-sort-bar select {
    margin-left: auto;
    margin: auto;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .vs-product-box .actions .vs-btn {
    height: 38px;
    padding: 8px 15px;
  }
}

/*------------------- 4.14. Products  -------------------*/
.product-thumb img {
  cursor: pointer;
}

.product-big-img {
  --gap: 50px;
  position: relative;
}

.product-big-img img {
  border-radius: 20px;
}

.product-big-img .add_to_wishlist {
  position: absolute;
  top: calc(var(--gap) - 5px);
  right: var(--gap);
}

.product-big-img .product-thumb-area {
  width: calc(100% - var(--gap) * 2);
  position: absolute;
  bottom: var(--gap);
  left: var(--gap);
}

.product-about {
  margin-top: -1.5rem;
}

.product-about .woocommerce-product-rating {
  display: inline-block;
  margin-bottom: 8px;
}

.product-about .star-rating {
  display: inline-block;
  vertical-align: middle;
}

.product-about .woocommerce-review-link {
  color: var(--body-color);
}

.product-about .woocommerce-review-link:hover {
  color: var(--theme-color);
}

.product-about .price {
  font-size: 30px;
}

.product-about .actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 30px;
  margin-bottom: 25px;
}

.product-about .actions .quantity {
  margin-right: 25px;
}

.product_meta {
  font-weight: 500;
  font-size: 18px;
  text-transform: capitalize;
  font-family: var(--para-font);
}

.product_meta>span {
  display: block;
  margin-bottom: 5px;
  color: var(--title-color);
}

.product_meta>span:last-child {
  margin-bottom: 0;
}

.product_meta>span a {
  color: inherit;
}

.product_meta>span a:hover {
  color: var(--theme-color);
}

.product_meta>span>a,
.product_meta>span>span {
  position: relative;
  color: var(--body-color);
}

.product_meta>span>a:after,
.product_meta>span>span:after {
  content: ",";
  margin-right: 5px;
}

.product_meta>span>a:last-child:after,
.product_meta>span>span:last-child:after {
  display: none;
}

.product_meta>span>a:first-child,
.product_meta>span>span:first-child {
  margin-left: 7px;
}

.actions.d-flex {
  flex-wrap: wrap;
  gap: 12px;
}

.product-tab-style1 {
  border-bottom: 1px solid #ececec;
  position: relative;
}

.product-tab-style1 a {
  color: var(--title-color);
  font-size: 20px;
  font-weight: 600;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  line-height: 1;
  padding: 0 0 13px 0;
  margin-right: 45px;
  font-family: var(--title-font);
  position: relative;
}

.product-tab-style1 a.active {
  color: var(--theme-color);
}

.product-tab-style1 .indicator {
  position: absolute;
  left: 0;
  top: auto !important;
  height: 0 !important;
  bottom: -1px;
  -webkit-transition: all ease 0.4s;
  transition: all ease 0.4s;
  border-bottom: 2px solid var(--theme-color);
}

.product-desc-area p {
  margin-bottom: 25px;
}

.product-desc-area ul,
.product-desc-area .check-list {
  margin-bottom: 25px;
}

#description>p:first-child {
  margin-top: -7px;
}

/* Large devices */
@media (max-width: 1199px) {
  .product-about .price {
    font-size: 26px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .product-about {
    margin-top: 0;
  }

  .product-about .price {
    font-size: 24px;
  }

  .product-tab-style1 a {
    font-size: 18px;
    margin-right: 30px;
  }

  .price,
  .amount {
    font-size: 16px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .product-about .product-title {
    margin-bottom: 7px;
  }

  .product-about .price {
    font-size: 20px;
  }

  .product-about .actions .quantity {
    margin-right: 0;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .product-tab-style1 .indicator {
    display: none;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .product-big-img {
    --gap: 20px;
  }

  .quantity {
    width: 110px;
  }

  .quantity input {
    width: 36px;
    height: 36px;
  }

  .quantity .qut-btn {
    width: 36px;
  }

  .actions.d-flex {
    gap: 12px;
  }
}

/*------------------- 4.15. Cart  -------------------*/
.woocommerce-cart-form {
  text-align: center;
}

.cart_table {
  border: 1px solid #eaf0f2;
  margin-bottom: 45px;
}

.cart_table thead {
  background-color: #ecf0f1;
}

.cart_table thead th {
  border: none !important;
}

.cart_table td:before,
.cart_table th {
  font-family: var(--title-font);
  color: var(--title-color);
  font-weight: 600;
  border: none;
  padding: 27px 15px;
}

.cart_table td:before {
  content: attr(data-title);
  position: absolute;
  left: 15px;
  top: 50%;
  vertical-align: top;
  padding: 0;
  transform: translateY(-50%);
  display: none;
}

.cart_table td {
  border: none;
  border-bottom: 1px solid #f3f3f3;
  color: #8b8b8b;
  padding: 20px 10px;
  position: relative;
  vertical-align: middle;
}

.cart_table .product-quantity {
  color: var(--title-color);
}

.cart_table .product-quantity input {
  position: relative;
  top: -2px;
}

.cart_table .cart-productname {
  font-weight: 400;
  font-family: var(--body-font);
  color: var(--body-color);
}

.cart_table .cart-productimage {
  display: inline-block;
  border: 2px solid var(--smoke-color);
}

.cart_table .remove {
  color: var(--theme-color);
  font-size: 18px;
}

.cart_table .quantity {
  display: inline-flex;
  align-items: center;
  border: none;
}

.cart_table .qut-btn {
  border: 2px solid var(--smoke-color);
  background-color: transparent;
  color: #b8c6d0;
  padding: 0;
  width: 30px;
  height: 30px;
  line-height: 25px;
  font-size: 16px;
  border-radius: 4px;
}

.cart_table .qut-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.cart_table .qty-input {
  vertical-align: middle;
  border: 2px solid var(--smoke-color);
  width: 70px;
  height: 30px;
  font-size: 14px;
  text-align: center;
  color: #2c3e50;
  font-weight: 700;
  margin: 0 10px;
  border-radius: 4px;
  /* Firefox */
}

.cart_table .qty-input::-webkit-outer-spin-button,
.cart_table .qty-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.cart_table .qty-input[type=number] {
  -moz-appearance: textfield;
}

.cart_table .actions {
  text-align: right;
  vertical-align: middle;
}

.cart_table .actions>.vs-btn {
  font-size: 16px;
  padding: 11px 28px;
  margin-right: 15px;
}

.cart_table .actions>.vs-btn:last-child {
  margin-right: 0;
}

.cart_table .vs-cart-coupon {
  float: left;
  margin: 0;
  width: 455px;
  max-width: 100%;
  display: flex;
}

.cart_table .vs-cart-coupon input {
  height: 50px;
  width: calc(100% - 200px);
  margin-right: 10px;
  border-radius: 30px;
}

.cart_table .vs-cart-coupon .vs-btn {
  font-size: 16px;
  padding: 11px 22px;
  width: max-content;
}

.cart_table .actions .vs-btn {
  display: inline;
}

.cart_totals {
  border: 1px solid #ecf0f1;
}

.cart_totals th,
.cart_totals td {
  vertical-align: top;
  padding: 20px 20px;
  border: none;
  border-bottom: 1px solid #ecf0f1;
  font-size: 14px;
  color: var(--title-color);
  width: 55%;
}

.cart_totals th:first-child,
.cart_totals td:first-child {
  width: 45%;
  background-color: #f9fbfb;
  font-weight: 700;
  font-size: 14px;
  color: #333333;
}

.cart_totals .shipping-calculator-button {
  display: inline-block;
  border-bottom: 1px solid;
  color: var(--title-color);
  font-weight: 700;
}

.cart_totals .shipping-calculator-button:hover {
  color: var(--theme-color);
}

.cart_totals .woocommerce-shipping-destination {
  margin-bottom: 10px;
}

.cart_totals .woocommerce-shipping-methods {
  margin-bottom: 0;
}

.cart_totals .woocommerce-shipping-methods input[type=radio]~label::before {
  padding-right: 1.2px;
  padding-top: 0;
}

.cart_totals .shipping-calculator-form {
  display: none;
}

.cart_totals .shipping-calculator-form p:first-child {
  margin-top: 20px;
}

.cart_totals .shipping-calculator-form p:last-child {
  margin-bottom: 0;
}

.cart_totals .shipping-calculator-form .vs-btn {
  padding: 9px 20px;
  min-width: 120px;
}

.cart_totals .amount {
  font-weight: 700;
}

.cart_totals .order-total .amount {
  color: var(--theme-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .cart_table th {
    padding: 23px 8px;
    font-size: 14px;
  }

  .cart_table .cart-productname {
    font-size: 14px;
  }

  .cart_table .vs-cart-coupon {
    width: 100%;
    margin-bottom: 20px;
  }

  .cart_table .actions {
    text-align: center;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .cart_table {
    text-align: left;
    min-width: auto;
    border-collapse: separate;
    border-spacing: 0 20px;
    border: none;
  }

  .cart_table thead {
    display: none;
  }

  .cart_table td {
    padding: 15px;
    display: block;
    width: 100%;
    padding-left: 25%;
    text-align: right;
    border: 1px solid #f3f3f3;
    border-bottom: none;
  }

  .cart_table td::before {
    display: block;
  }

  .cart_table td:last-child {
    border-bottom: 1px solid #f3f3f3;
  }

  .cart_table td.actions {
    padding-left: 15px;
    text-align: center;
  }

  .cart_table td.actions>.vs-btn {
    margin-top: 10px;
    margin-right: 0;
    display: block;
    width: max-content;
    margin-left: auto;
    margin-right: auto;
  }

  .cart_table td.actions>.vs-btn:last-child {
    margin-right: auto;
  }

  .cart_table .vs-cart-coupon {
    width: 100%;
    text-align: center;
    float: none;
    justify-content: center;
    display: block;
    padding-bottom: 10px;
  }

  .cart_table .vs-cart-coupon input {
    width: 100%;
    margin-bottom: 10px;
  }

  .cart_totals th,
  .cart_totals td {
    padding: 15px 10px;
  }

  .cart_totals th:first-child,
  .cart_totals td:first-child {
    width: 17%;
    line-height: 1.4;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .cart_table .quantity {
    width: 150px;
  }
}

/*------------------- 4.16. Checkout  -------------------*/
.woocommerce-checkout .form-group {
  margin-bottom: var(--bs-gutter-x);
}

.woocommerce-checkout .select2-container--open .select2-dropdown--below {
  margin-top: 30px;
}

.woocommerce-checkout .select2-container--open .select2-dropdown--above {
  position: relative;
  bottom: -30px;
}

.woocommerce-checkout .select2-dropdown {
  border: 1px solid #e3e6e9;
  border-top: none;
}

.woocommerce-checkout .select2-container--default .select2-selection--single .select2-selection__rendered,
.woocommerce-checkout .form-control:focus {
  color: var(--body-color);
}

.woocommerce-form-login select,
.woocommerce-form-login .form-select,
.woocommerce-form-login .form-control,
.woocommerce-form-coupon select,
.woocommerce-form-coupon .form-select,
.woocommerce-form-coupon .form-control,
.woocommerce-checkout select,
.woocommerce-checkout .form-select,
.woocommerce-checkout .form-control {
  background-color: var(--white-color);
}

.select2-container--default .select2-selection--single {
  height: 60px;
  border: 1px solid #e3e6e9;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 60px;
  padding-left: 30px;
  padding-right: 25px;
}

.woocommerce-billing-fields .form-row {
  margin-bottom: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "\f107";
  font-family: var(--icon-font);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin: 0;
  border: none;
  top: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 60px;
  line-height: 60px;
  margin-right: 18px;
}

span.select2-selection.select2-selection--single:focus {
  outline: none;
}

.shipping-calculator-form .form-select,
.shipping-calculator-form .form-control {
  height: 40px;
  padding-left: 15px;
  font-size: 16px;
  border-radius: 10px;
  background-position: right 13px top 13px !important;
}

.shipping-calculator-form .vs-btn {
  font-size: 14px;
  padding: 0 20px;
  width: max-content;
  height: 40px;
}

.woocommerce-form-login {
  display: none;
  padding: 30px 30px 25px 30px;
  margin-bottom: 30px;
}

.woocommerce-form-login .form-group {
  margin-bottom: 20px;
}

.woocommerce-form-login .form-group:last-child {
  margin-bottom: 0;
}

.woocommerce-form-login label {
  margin-bottom: 0.3em;
}

.woocommerce-form-coupon {
  display: none;
  padding: 30px;
}

.woocommerce-form-coupon .form-group {
  margin-bottom: 20px;
}

.woocommerce-form-coupon .form-group:last-child {
  margin-bottom: 0;
}

.woocommerce-checkout .shipping_address {
  display: none;
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: none;
  vertical-align: top;
  padding: 5px 0;
  font-size: 14px;
  font-weight: 700;
  color: #2c3e50;
}

.checkout-ordertable ul {
  margin: 0;
  padding: 0;
}

.checkout-ordertable .order-total .amount {
  color: var(--theme-color);
}

.checkout-ordertable input[type=hidden]~label {
  color: var(--theme-color);
}

.woocommerce-checkout .form-group input:not(:last-child) {
  margin-bottom: var(--bs-gutter-x);
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: 1px solid #ededed;
  text-align: right;
  padding: 5px 20px;
}

.checkout-ordertable th {
  text-align: left;
}

.woocommerce-checkout-payment {
  text-align: left;
}

.woocommerce-checkout-payment ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.woocommerce-checkout-payment ul li {
  padding-top: 10px;
  border-bottom: 1px solid #d8d8d8;
  border-radius: 4px;
  font-size: 16px;
}

.woocommerce-checkout-payment ul input[type=radio]~label {
  margin-bottom: 15px;
  color: var(--body-color);
  width: 100%;
}

.woocommerce-checkout-payment ul input[type=radio]~label img {
  margin-left: 30px;
  float: right;
  position: relative;
  top: 1px;
}

.woocommerce-checkout-payment .place-order {
  padding-top: 30px;
}

.woocommerce-checkout-payment .payment_box {
  color: #a1b1bc;
  background-color: #ecf0f1;
  border: 1px solid #d8d8d8;
  border-bottom: none;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  display: none;
}

.woocommerce-checkout-payment .payment_box p {
  margin: 0;
}

.vs-checkout-wrapper form.woocommerce-form {
  margin-bottom: 25px;
}

/* Small devices */
@media (max-width: 767px) {
  tfoot.checkout-ordertable th {
    display: none;
  }

  .woocommerce-checkout-payment ul input[type=radio]~label img {
    max-width: 150px;
  }

  .checkout-ordertable th,
  .checkout-ordertable td {
    padding-left: 25%;
  }
}

/*------------------- 4.17. Wishlist  -------------------*/
.tinv-wishlist input[type=checkbox] {
  display: inline-block;
  opacity: 1;
  visibility: visible;
  width: auto;
  height: auto;
}

.tinv-wishlist .cart-empty {
  padding: 12px 25px;
  background-color: #eee;
  border-radius: 5px;
  font-weight: 700;
  font-size: 14px;
}

.tinv-wishlist p.return-to-shop .button {
  display: inline-block;
  background-color: var(--theme-color);
  color: #fff;
  font-size: 14px;
  padding: 10px 25px;
  border-radius: 4px;
  margin-top: 10px;
  font-weight: 700;
}

.tinv-wishlist p.return-to-shop .button:Hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

.tinv-wishlist table th {
  color: var(--title-color);
}

.tinv-wishlist table td,
.tinv-wishlist table th {
  padding: 10px;
  border: 1px solid var(--border-color);
  text-align: center;
}

.tinv-wishlist .product-cb,
.tinv-wishlist .product-remove {
  width: 40px;
  text-align: center;
}

.tinv-wishlist .product-thumbnail {
  width: 110px;
}

.tinv-wishlist .stock.in-stock {
  margin-bottom: 0;
}

.tinv-wishlist ins {
  text-decoration: none;
}

.tinv-wishlist .product-remove button {
  border: none;
  height: 22px;
  width: 22px;
  text-align: center;
  font-size: 12px;
  line-height: 22px;
}

.tinv-wishlist .tinvwl-mobile {
  display: none;
}

.tinv-wishlist .social-buttons {
  display: flex;
  max-width: 295px;
  margin-left: auto;
  align-items: center;
}

.tinv-wishlist .social-buttons ul {
  padding-left: 0;
  margin-bottom: 0;
  margin-left: auto;
}

.tinv-wishlist table.tinvwl-table-manage-list {
  font-size: 14px;
}

.tinv-wishlist table.tinvwl-table-manage-list button {
  border-radius: 5px;
}

.tinv-wishlist .product-stock i {
  margin-right: 5px;
}

.tinv-wishlist .tinv-modal .icon_big_times {
  margin-bottom: 5px;
  color: var(--theme-color);
}

.tinv-wishlist button.button {
  border: none;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 700;
  background-color: var(--theme-color);
  color: #fff;
  padding: 1px 15px;
}

.tinv-wishlist button.button i {
  font-size: 14px !important;
  margin-right: 3px !important;
}

.tinv-wishlist th,
.tinv-wishlist td.product-name {
  font-size: 16px;
  font-weight: 700;
  font-family: var(--title-font);
}

.tinv-wishlist td.product-name a {
  color: var(--title-color);
}

.tinv-wishlist td.product-name a:hover {
  color: var(--theme-color);
}

.tinv-wishlist td.product-price del {
  font-size: 0.9em;
}

.tinv-wishlist .social-buttons>span {
  font-weight: 700;
  margin-right: 10px;
  font-family: var(--title-font);
  color: var(--title-color);
}

.tinv-wishlist .social-buttons li {
  display: inline-block;
}

.tinv-wishlist .social-buttons li a.social {
  background-color: var(--theme-color);
  color: #fff;
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  border-radius: 50px;
  margin-left: 3px;
}

.tinv-wishlist .social-buttons li a.social:first-child {
  margin-left: 0;
}

.tinv-wishlist .social-buttons li a.social i {
  line-height: inherit;
}

.tinv-wishlist .social-buttons li a.social:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .tinvwl-full {
    display: none;
  }

  .tinv-wishlist .tinvwl-mobile {
    display: block;
  }

  .tinvwl-txt {
    display: none;
  }

  .product-stock {
    width: 40px;
    text-align: center;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .tinv-wishlist table {
    table-layout: fixed;
  }

  .tinv-wishlist table.tinvwl-table-manage-list tbody td.product-remove,
  .tinv-wishlist table.tinvwl-table-manage-list thead th:not(.product-name) {
    display: none;
  }

  .tinv-wishlist table.tinvwl-table-manage-list tbody td {
    display: block;
    width: 100% !important;
    text-align: center;
  }

  .product-name {
    text-align: center;
  }

  .tinv-wishlist table td,
  .tinv-wishlist table th {
    border-bottom: none;
  }

  .tinv-wishlist table tbody {
    border-bottom: 1px solid var(--border-color);
  }

  .tinv-wishlist .social-buttons {
    max-width: 100%;
    margin-left: unset;
    flex-direction: column;
  }

  .tinv-wishlist .social-buttons ul {
    margin-left: unset;
    margin-top: 5px;
  }

  .tinvwl-txt {
    display: inline-block;
  }
}

/*------------------- 4.18. simple section  -------------------*/
.img-box-1 img {
  border-radius: 30px;
}

.img-box-2 {
  border-radius: 50%;
  position: relative;
  width: 558px;
  height: 558px;
  border: 2px dashed rgb(255, 228, 222);
}

.img-box-2 img {
  border-radius: 50%;
}

.img-box-2 .small-img {
  width: 250px;
  height: 250px;
  position: absolute;
  bottom: 30px;
  right: 0;
}

.img-box-2 .big-img {
  position: absolute;
  top: -7px;
  left: -7px;
  max-width: calc(100% - 46px);
}

.feature-box {
  display: flex;
}

.feature-box .feature-icon {
  min-width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  margin-right: 20px;
  background-color: var(--white-color);
  box-shadow: 0px 9px 46px 0px rgba(75, 83, 100, 0.14);
  border-radius: 50%;
  display: inline-block;
  transition: all ease 0.4s;
}

.feature-box .feature-icon img {
  transition: all ease 0.4s;
  filter: none;
}

.feature-box .feature-info {
  margin-bottom: 35px;
}

.feature-box:last-child .feature-info {
  margin-bottom: 0;
}

.feature-box .feature-title {
  margin-top: -0.25rem;
  margin-bottom: 7px;
}

.feature-box .feature-text {
  margin-bottom: 0;
}

.feature-box:hover .feature-icon {
  background-color: var(--theme-color);
  color: var(--white-color);
  box-shadow: none;
}

.feature-box:hover .feature-icon img {
  filter: brightness(0) invert(1);
}

.simple-section {
  position: relative;
  padding-left: 120px;
  /* Extra large devices */
  /* Large devices */
}

@media (max-width: 1500px) {
  .simple-section {
    padding-left: 15px;
  }
}

@media (max-width: 1199px) {
  .simple-section {
    padding: 0 15px;
  }
}

.simple-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 945px;
  height: 540px;
  background: #DBEAF7;
  z-index: -11;
}

.simple-section .img-box-1 {
  margin-right: -36px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .simple-section .img-box-1 {
    margin-right: 0;
    margin-bottom: 30px;
  }
}

.simple-section .img-box-1 img {
  width: 100%;
}

.simple-section .content-box {
  position: relative;
  border-radius: 30px 0px 0px 30px;
  background: #F9F5EF;
  padding: 117px 161px 76px;
  margin-left: -64px;
  z-index: -1;
  /* Medium Large devices */
  /* Large devices */
  /* Extra small devices */
}

@media (max-width: 1399px) {
  .simple-section .content-box {
    padding: 70px 80px 70px 140px;
  }
}

@media (max-width: 1199px) {
  .simple-section .content-box {
    margin-left: 0;
    padding: 50px;
    border-radius: 30px;
  }
}

@media (max-width: 416px) {
  .simple-section .content-box {
    padding: 40px 15px;
  }
}

.simple-section .content-box .title-box {
  max-width: 595px;
}

.simple-section .content-box .image {
  position: absolute;
  bottom: 0;
  right: 0;
}

@media (max-width: 1800px) {
  .simple-section .content-box .image {
    display: none;
  }
}

.wave {
  position: absolute;
  top: 103px;
  left: 73px;
}

.letter {
  position: absolute;
  bottom: 26px;
  left: 94px;
}

.simple-block {
  position: relative;
}

.simple-block::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 38px;
  width: 4px;
  height: 170px;
  background-color: rgb(219, 234, 247);
}

.simple-block .feature-box-two {
  display: flex;
  margin-bottom: 35px;
}

.simple-block .feature-box-two .feature-icon {
  position: relative;
  min-width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  margin-right: 20px;
  border-radius: 50%;
  display: inline-block;
  transition: all ease 0.4s;
  z-index: 99;
}

.simple-block .feature-box-two .feature-icon img {
  transition: all ease 0.4s;
  filter: none;
}

.simple-block .feature-box-two:last-child .feature-info {
  margin-bottom: 0;
}

.simple-block .feature-box-two .feature-title {
  margin-top: -0.25rem;
  margin-bottom: 7px;
}

.simple-block .feature-box-two .feature-text {
  margin-bottom: 0;
}

.mockup1 {
  padding-bottom: 310px;
}

/* Large devices */
@media (max-width: 1199px) {
  .img-box-2 {
    width: 100%;
    height: 450px;
  }

  .img-box-2 .small-img {
    width: 200px;
    height: 200px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .img-box-2 {
    width: 480px;
    height: 480px;
    margin-left: auto;
    margin-right: auto;
  }

  .mockup1 {
    padding-bottom: 220px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .img-box-2 {
    width: 100%;
    height: calc(100vw - 35px);
  }

  .feature-box {
    flex-direction: column;
    align-items: flex-start;
  }

  .feature-box .feature-icon {
    margin-bottom: 20px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .img-box-2 .big-img {
    top: 0;
    left: 0;
    max-width: calc(100% - 10px);
  }

  .img-box-2 .small-img {
    display: none;
  }
}

.why-section-two {
  position: relative;
  /* Extra small devices */
  /* Medium devices */
}

@media (max-width: 575px) {
  .why-section-two #choose-tabContent {
    margin-top: 10px;
  }
}

@media (max-width: 991px) {
  .why-section-two {
    margin-top: 0;
  }
}

.why-section-two .glob {
  position: absolute;
  right: 91px;
  top: 129px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .why-section-two .glob {
    display: none !important;
  }
}

.why-section-two .star {
  position: absolute;
  bottom: 58px;
  left: 199px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .why-section-two .star {
    display: none !important;
  }
}

.why-section-two .text-center {
  /* Medium devices */
}

@media (max-width: 991px) {
  .why-section-two .text-center {
    text-align: left !important;
  }
}

.why-section-two .nav-tabs {
  /* Extra small devices */
}

@media (max-width: 575px) {
  .why-section-two .nav-tabs {
    border: 0;
  }
}

.why-section-two .choose-tab {
  margin-bottom: 53px;
  /* Small devices */
}

@media (max-width: 767px) {
  .why-section-two .choose-tab {
    margin-bottom: 0;
  }
}

.why-section-two .choose-tab .choose-btn {
  padding: 12px 50px;
  /* Medium Large devices */
  /* Small devices */
  /* Extra small devices */
}

@media (max-width: 1399px) {
  .why-section-two .choose-tab .choose-btn {
    padding: 12px 44px;
  }
}

@media (max-width: 767px) {
  .why-section-two .choose-tab .choose-btn {
    padding: 12px 39px;
  }
}

@media (max-width: 575px) {
  .why-section-two .choose-tab .choose-btn {
    margin-bottom: 15px;
    width: max-content;
    padding: 8px 19px;
  }

  .why-section-two .choose-tab .choose-btn.active {
    background-color: var(--theme-color);
  }
}

.why-section-two .content-box {
  /* Large devices */
}

@media (max-width: 1199px) {
  .why-section-two .content-box {
    max-width: 673px;
  }
}

.why-section-two .content-box .title {
  font-size: 30px;
  color: var(--title-color);
  font-weight: 700;
  line-height: 40px;
  margin-bottom: 12px;
  /* Extra small devices */
}

@media (max-width: 416px) {
  .why-section-two .content-box .title {
    font-size: 26px;
    line-height: 36px;
  }
}

.why-section-two .content-box p {
  margin-bottom: 27px;
}

.why-section-two .content-box .check-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  /* Small devices */
}

@media (max-width: 767px) {
  .why-section-two .content-box .check-list {
    flex-direction: column;
    align-items: baseline;
  }
}

.why-section-two .content-box .check-list ul li {
  font-size: 18px;
  color: var(--body-color);
  padding-left: 27px;
  margin-bottom: 12px;
}

.why-section-two .content-box .check-list ul li:last-child {
  margin-bottom: 0;
}

.why-section-two .content-box .check-list ul li::before {
  width: 18px;
  height: 18px;
  line-height: 18px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
}

.why-section-two .content-box-two .sec-title {
  /* Extra small devices */
}

@media (max-width: 575px) {
  .why-section-two .content-box-two .sec-title {
    font-size: 40px;
  }
}

.why-section-two .why-images {
  padding-right: 30px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .why-section-two .why-images {
    padding-right: 0;
    margin-bottom: 50px;
  }
}

.why-section-two .why-images img {
  width: 100%;
  border-radius: 20px;
}

.ml-auto {
  margin-left: auto;
  /* Medium devices */
}

@media (max-width: 991px) {
  .ml-auto {
    margin: 30px auto 0;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }
}

/*------------------- 4.19. Service  -------------------*/
.service-section {
  position: relative;
}

.service-card-inner {
  text-align: center;
  border-radius: 50px;
  padding: 45px 40px 43px 40px;
  background-color: var(--theme-color);
}

.service-card .sr-icon {
  position: relative;
  z-index: 1;
  max-width: 100px;
  max-height: 100px;
  margin: 0 auto 25px auto;
}

.service-card .sr-icon:before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background-color: var(--white-color);
  z-index: -1;
  transition: all ease 0.4s;
  border-radius: 10px;
  opacity: 0;
  visibility: hidden;
}

.service-card .sr-title {
  font-weight: bold;
  color: var(--title-color);
}

.service-card-inner h2:hover {
  color: #fff;
}

.service-card .sr-title a {
  color: inherit;
  transition: all ease 0.4s;
}

.service-card .sr-title a:hover {
  color: var(--title-color);
}

.service-card .sr-text {
  margin-bottom: 0;
  color: var(--body-color);
  transition: all ease 0.4s;
}

.service-card .service-card-inner {
  transition: all ease 0.4s;
}

.service-card:hover .service-card-inner {
  background-color: var(--title-color);
}

.service-card:hover .sr-icon:before {
  opacity: 1;
  visibility: visible;
}

.service-card:hover .sr-text,
.service-card:hover .sr-title a {
  color: var(--white-color);
}

.service-card:nth-child(1) {
  --body-color: #9a6b5f;
  --title-color: #fcc68e;
  --theme-color: #ffe9e4;
}

.service-card:nth-child(2) {
  --title-color: var(--blue-color);
  --body-color: #767195;
  --theme-color: #edeaff;
}

.service-card:nth-child(3) {
  --title-color: var(--yellow-color);
  --body-color: #97855c;
  --theme-color: #fff3d8;
}

.service-card:nth-child(4) {
  --title-color: #5bd7ca;
  --body-color: #5e9892;
  --theme-color: #defdfa;
}

.service-box {
  position: relative;
  line-height: 1;
  border-radius: 30px;
  overflow: hidden;
}

.service-box .service-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  padding: 50px 50px 46px 50px;
  z-index: 3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  transition: 0.4s ease-in-out;
}

.service-box .service-icon {
  margin-bottom: 22px;
  width: 65px;
}

.service-box .service-title {
  margin-bottom: 8px;
  color: var(--white-color);
}

.service-box .service-title a {
  color: inherit;
}

.service-box .service-title a:hover {
  color: rgba(255, 255, 255, 0.8);
}

.service-box .vs-btn {
  margin-top: 10px;
}

.service-box .service-text {
  color: var(--white-color);
  height: 0;
  transform: translateY(40px);
  opacity: 0;
  visibility: hidden;
  transition: 0.4s ease-in-out;
}

.service-box:hover .service-content {
  background-color: var(--theme-color);
}

.service-box:hover .service-text {
  transform: translateY(0);
  height: 75px;
  opacity: 1;
  visibility: visible;
  margin-bottom: 25px;
  margin-top: 4px;
}

.service-box:hover .vs-btn {
  background-color: rgb(255, 255, 255);
  color: var(--theme-color);
}

.service-box:hover .vs-btn:hover {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.service-tab {
  border-bottom: none;
}

.service-tab .sr-icon-btn {
  background-color: var(--white-color);
}

.service-tab .sr-icon-btn:not(:last-child) {
  margin-bottom: 30px;
}

.sr-icon-btn {
  --gap: 15px;
  width: 179px;
  height: 172px;
  border-radius: 20px;
  padding: 15px;
  text-align: center;
  border: none;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  background-color: transparent;
  position: relative;
  z-index: 2;
  color: var(--theme-color);
  transition: 0.4s ease-in-out;
}

.sr-icon-btn::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border: 2px dashed #e9e0d1;
  border-radius: 20px;
  transition: 0.4s ease-in-out;
}

.sr-icon-btn::after {
  content: "";
  width: calc(100% - var(--gap) * 2);
  height: calc(100% - var(--gap) * 2);
  position: absolute;
  top: var(--gap);
  left: var(--gap);
  background-color: var(--theme-color);
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s ease-in-out;
}

.sr-icon-btn img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 10px;
  transition: 0.4s ease-in-out;
}

.sr-icon-btn:hover,
.sr-icon-btn.active {
  color: var(--white-color);
}

.sr-icon-btn:hover:after,
.sr-icon-btn.active:after {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
}

.sr-icon-btn:hover:before,
.sr-icon-btn.active:before {
  width: calc(100% - var(--gap) * 2);
  height: calc(100% - var(--gap) * 2);
  left: var(--gap);
  top: var(--gap);
}

.sr-icon-btn:hover img,
.sr-icon-btn.active img {
  filter: brightness(0) invert(1);
}

.service-tabcontent {
  height: 100%;
}

.service-tabcontent>.tab-pane {
  height: 100%;
}

.service-grid {
  height: 100%;
}

.service-grid>[class^=col]:first-child {
  height: 100%;
}

.service-grid .service-img {
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.service-grid .service-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  border-radius: 20px;
}

.service-grid .service-icon {
  margin-bottom: 25px;
}

.service-grid .service-text {
  margin-bottom: 25px;
}

.service-grid .service-title {
  text-transform: capitalize;
}

.service-grid .service-title a {
  color: inherit;
}

.service-grid .service-title a:hover {
  color: var(--theme-color);
}

.service-grid .check-list {
  margin-bottom: 30px;
}

/*----------- Service section-two ----------*/
.services-section-two .service-box-two.v2 {
  background: #EDEAFF;
}

.services-section-two .service-box-two.v2 .service-img .service-icon {
  background: #786ACF;
}

.services-section-two .service-box-two.v2 .service-content .service-title {
  color: #786ACF;
}

.services-section-two .service-box-two.v2 .service-content .vs-btn {
  background: #786ACF;
}

.services-section-two .service-box-two.v3 {
  background: #DEFDFA;
}

.services-section-two .service-box-two.v3 .service-img .service-icon {
  background: #00A796;
}

.services-section-two .service-box-two.v3 .service-content .service-title {
  color: #00A796;
}

.services-section-two .service-box-two.v3 .service-content .vs-btn {
  background: #00A796;
}

.services-section-two .title-area .sec-title {
  /* Extra small devices */
  /* Extra small devices */
}

@media (max-width: 575px) {
  .services-section-two .title-area .sec-title {
    font-size: 35px;
  }
}

@media (max-width: 416px) {
  .services-section-two .title-area .sec-title {
    font-size: 25px;
  }
}

.services-section-two .shape-sun {
  position: absolute;
  top: 120px;
  left: 100px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .services-section-two .shape-sun {
    display: none !important;
  }
}

.services-section-two .shape-boll {
  position: absolute;
  bottom: 80px;
  right: 140px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .services-section-two .shape-boll {
    display: none !important;
  }
}

.service-box-two {
  border-radius: 20px;
  background: #FFE9E4;
}

.service-box-two .service-img {
  position: relative;
}

.service-box-two .service-img img {
  border-radius: 20px;
  width: 100%;
}

.service-box-two .service-img .service-icon {
  position: absolute;
  top: 40px;
  left: 41px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 20px;
  background: #FE5D37;
}

.service-box-two .service-img .service-icon img {
  border-radius: 0;
  width: unset;
}

.service-box-two .service-content {
  padding: 35px 40px 31px;
}

.service-box-two .service-content .service-title {
  color: #FE5D37;
  margin-bottom: 7px;
}

.service-box-two .service-content .service-title a {
  color: inherit;
}

.service-box-two .service-content .vs-btn {
  background-color: #FE5D37;
}

.service-box-two .service-content .service-text {
  margin-bottom: 21px;
}

/*----------- Service Details ----------*/
.service-details {
  padding-right: 30px;
}

.service-details .service-img {
  border-radius: 30px;
  overflow: hidden;
  margin-bottom: 40px;
}

.service-details .service-img img {
  border-radius: 30px;
}

.single-title {
  font-size: 40px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .service-box .service-content {
    padding: 25px 25px 23px 25px;
  }

  .service-box:hover .sr-btn {
    display: none;
  }

  .service-box:hover .service-text {
    margin-bottom: 0;
    height: 100px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .service-card-inner {
    border-radius: 30px;
    padding: 45px 15px 43px 15px;
  }

  .service-tabcontent {
    margin-top: 30px;
  }

  .service-tab {
    border-bottom: none;
    display: flex;
    gap: 15px;
    height: 140px;
  }

  .service-tab .sr-icon-btn:not(:last-child) {
    margin-bottom: 0;
  }

  .sr-icon-btn {
    --gap: 10px;
    width: auto;
    height: auto;
    flex: 1;
    align-self: stretch;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    border-radius: 10px;
  }

  .sr-icon-btn:before,
  .sr-icon-btn:after {
    border-radius: 10px;
  }

  .sr-icon-btn img {
    max-width: 50px;
    margin-bottom: 5px;
  }

  .service-details {
    padding-right: 15px;
  }

  .single-title {
    font-size: 36px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .service-card-inner {
    border-radius: 50px;
    padding: 45px 40px 43px 40px;
  }

  .service-box:hover .sr-btn {
    display: inline-block;
  }

  .service-box:hover .service-text {
    margin-bottom: 10px;
    height: 75px;
  }

  .service-tab {
    height: 100px;
  }

  .sr-icon-btn {
    font-weight: 400;
  }

  .sr-icon-btn img {
    max-width: 40px;
  }

  .service-details {
    padding-right: 0;
  }

  .single-title {
    font-size: 32px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .service-box .service-content {
    padding: 50px 40px 40px 40px;
  }

  .service-box:hover .sr-btn {
    display: inline-block;
  }

  .service-box:hover .service-text {
    margin-bottom: 10px;
  }

  .service-tab {
    height: 130px;
  }

  #service-tab1 {
    margin-bottom: 15px;
  }

  .service-grid {
    height: auto;
  }

  .service-grid .service-img {
    height: 350px;
    margin-bottom: 30px;
  }

  .single-title {
    font-size: 28px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .single-title {
    font-size: 24px;
  }

  .service-tab {
    height: 110px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .service-card-inner {
    border-radius: 30px;
    padding: 45px 30px 43px 30px;
  }

  .service-box .service-content {
    padding: 25px 25px 23px 25px;
  }
}

@media (max-width: 350px) {
  .service-box .service-text {
    font-size: 14px;
  }

  .service-box:hover .service-text {
    height: 65px;
  }

  .service-tab {
    height: 80px;
  }

  .sr-icon-btn {
    --gap: 1px;
    padding: 2px;
    border-radius: 5px;
    font-size: 14px;
  }

  .sr-icon-btn:before,
  .sr-icon-btn:after {
    border-radius: 5px;
  }

  .sr-icon-btn img {
    max-width: 25px;
    margin-bottom: 3px;
    border-radius: 5px;
  }
}

/*------------------- 4.20. About  -------------------*/
.shape-slider-area {
  position: relative;
  padding-left: 50px;
}

.simple-arrow {
  background-color: transparent;
  border: none;
  width: 50px;
  height: 50px;
  color: var(--title-color);
  font-size: 28px;
}

.simple-arrow:hover {
  color: var(--theme-color);
}

.shape-slider-area .arrow-left,
.shape-slider-area .arrow-right {
  position: absolute;
  top: 50%;
  z-index: 3;
}

.shape-slider-area .arrow-left {
  left: 58px;
}

.shape-slider-area .arrow-right {
  top: 57%;
  right: 64px;
}

#sliderOne .slide-item {
  padding-top: 40px;
  position: relative;
}

#sliderOne .line-1,
#sliderOne .line-2 {
  position: absolute;
  top: 0;
}

#sliderOne .line-1 {
  left: 40%;
}

#sliderOne .line-2 {
  left: 25%;
}

.about-form-box {
  padding: 52px 60px 60px 60px;
  border-radius: 30px;
}

.about-form-box .form-title,
.about-form-box .form-text {
  color: var(--white-color);
}

.about-form-box .form-text {
  margin-bottom: 30px;
}

.call-media {
  display: flex;
  align-items: center;
  border-left: 3px solid var(--theme-color);
  box-shadow: 0 0 30px 3px rgba(0, 0, 0, 0.05);
  padding: 20px 20px 20px 30px;
  margin: 0 0 40px 0;
  max-width: 350px;
  text-align: left;
}

.call-media .media-icon {
  font-size: 30px;
  color: var(--theme-color);
  margin-right: 20px;
}

.call-media .media-label {
  font-size: 16px;
  text-transform: uppercase;
  font-family: var(--title-font);
  font-weight: 500;
  letter-spacing: 0.02em;
  color: #b3b3b3;
  display: block;
  margin: 0 0 5px 0;
}

.call-media .media-info {
  font-size: 30px;
  font-family: var(--body-font);
  margin: 0;
  line-height: 1;
  font-weight: 500;
  color: var(--title-color);
}

.about-section-two {
  position: relative;
}

.about-section-two .tadi {
  position: absolute;
  top: 90px;
  left: 50px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .about-section-two .tadi {
    display: none !important;
  }
}

.about-section-two .anim-image {
  position: absolute;
  bottom: 165px;
  left: -100px;
}

.about-section-two .anim-img {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -111;
}

.about-section-two .content-box {
  border-radius: 20px;
  background: var(--white-color);
  padding: 57px 59px 51px;
  margin-left: 15px;
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}

@media (max-width: 1199px) {
  .about-section-two .content-box {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .about-section-two .content-box {
    padding: 57px 48px 51px;
  }
}

@media (max-width: 416px) {
  .about-section-two .content-box {
    padding: 57px 20px 51px;
    margin-left: 0;
  }
}

.about-section-two .content-box .title-area {
  margin-bottom: 18px;
}

.about-section-two .content-box p {
  margin-bottom: 21px;
}

.about-section-two .content-box .check-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  /* Large devices */
  /* Small devices */
}

@media (max-width: 1199px) {
  .about-section-two .content-box .check-list {
    justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .about-section-two .content-box .check-list {
    margin-bottom: 25px;
    flex-direction: column;
    align-items: baseline;
  }
}

.about-section-two .content-box .check-list ul {
  /* Large devices */
  /* Small devices */
}

@media (max-width: 1199px) {
  .about-section-two .content-box .check-list ul {
    margin-right: 40px;
  }
}

@media (max-width: 767px) {
  .about-section-two .content-box .check-list ul {
    margin-right: 0;
    margin-bottom: 0;
  }
}

.about-section-two .content-box .check-list ul li {
  font-size: 18px;
  color: var(--body-color);
  padding-left: 27px;
  margin-bottom: 12px;
}

.about-section-two .content-box .check-list ul li::before {
  width: 18px;
  height: 18px;
  line-height: 18px;
  top: 3px;
  font-size: 10px;
}

.about-section-two .about-img {
  position: relative;
  padding-right: 15px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .about-section-two .about-img {
    padding-right: 0;
  }
}

.about-section-two .about-img::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 200px;
}

.about-section-two .about-img img {
  border-radius: 15px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .about-section-two .about-img img {
    width: 100%;
  }
}

.about-section-two .about-img .image-one {
  position: relative;
  z-index: 99;
}

.about-section-two .about-img .image-three {
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .about-section-two .about-img .image-three {
    display: none;
  }
}

.about-section-two .about-img .image {
  position: absolute;
  bottom: 0;
  left: -150px;
  border: 10px solid var(--white-color);
  z-index: 99;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Extra small devices */
  /* Extra small devices */
}

@media (max-width: 1680px) {
  .about-section-two .about-img .image {
    left: -60px;
  }
}

@media (max-width: 1399px) {
  .about-section-two .about-img .image {
    left: -25px;
    bottom: 10px;
    width: 320px;
  }
}

@media (max-width: 1199px) {
  .about-section-two .about-img .image {
    width: unset;
    left: 15px;
  }
}

@media (max-width: 575px) {
  .about-section-two .about-img .image {
    left: 10px;
  }
}

@media (max-width: 416px) {
  .about-section-two .about-img .image {
    display: none;
  }
}

.about-section-two .two-btns .vs-btn {
  /* Extra small devices */
}

@media (max-width: 575px) {
  .about-section-two .two-btns .vs-btn {
    width: 100%;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .shape-slider-area .arrow-left {
    left: 0;
  }

  .shape-slider-area .arrow-right {
    right: 0;
  }

  .shape-slider-area {
    max-width: 600px;
    padding-left: 0;
    margin: 0 auto;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  #sliderOne .slide-item {
    padding-top: 10px;
  }

  #sliderOne .line-2 {
    display: none;
  }

  .call-media {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .about-form-box {
    padding: 40px 20px;
    border-radius: 20px;
  }

  .call-media {
    padding: 15px 15px 15px 20px;
    margin: 0 auto 30px auto;
  }

  .call-media .media-info {
    font-size: 26px;
  }

  .call-media .media-label {
    font-size: 14px;
  }

  .call-media .media-icon {
    font-size: 28px;
    margin-right: 15px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .simple-arrow {
    font-size: 22px;
  }

  .shape-slider-area .arrow-left,
  .shape-slider-area .arrow-right {
    top: 45%;
  }

  .shape-slider-area .arrow-right {
    top: 50%;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  #sliderOne .line-1 {
    display: none;
  }
}

/*------------------- 4.21. Counter  -------------------*/
.connter_icon {
  font-size: 40px;
  color: var(--theme-color);
  margin: 0 0 17px 0;
  transition: all ease 0.4s;
}

.connter_icon i {
  line-height: 1;
}

.counter-section {
  position: relative;
}

.counter-number {
  font-size: 72px;
  line-height: 1;
  margin-bottom: 8px;
  margin-top: -12px;
  font-weight: bold;
  color: var(--title-color);
}

.counter-text {
  margin-bottom: 0;
}

.counter-box {
  display: flex;
}

.counter-box .counter-icon {
  width: 60px;
  margin-right: 25px;
}

.counter-box .counter-number {
  margin-bottom: 0;
  color: var(--white-color);
}

.counter-box .counter-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--white-color);
}

.counter-section-two {
  position: relative;
  z-index: -111;
}

.counter-section-two::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(135, 201, 13, 0.8);
  z-index: -11;
}

.counter-section-two::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(16, 55, 65, 0.2);
  z-index: -1;
}

.counter-section-two .grass {
  position: absolute;
  top: -59px;
  right: 69px;
  z-index: -111;
}

.counter-box-two {
  display: flex;
  align-items: end;
  position: relative;
  z-index: 99;
}

.counter-box-two .counter-icon {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 20px;
  background: var(--white-color);
  margin-right: 21px;
}

.counter-box-two .counter-number {
  margin-bottom: 0;
  color: var(--white-color);
  font-size: 40px;
  font-weight: 700;
  display: inline;
}

.counter-box-two .counter-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--white-color);
  margin-top: 5px;
}

/* Large devices */
@media (max-width: 1199px) {
  .counter-number {
    font-size: 60px;
    font-weight: 600;
  }

  .counter-text {
    font-weight: 500;
  }

  .counter-box {
    justify-content: center;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .counter-number {
    font-size: 50px;
    margin-bottom: 0;
    margin-top: -7px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .counter-number {
    font-size: 36px;
    margin-top: -5px;
  }
}

/*------------------- 4.22. Class  -------------------*/
.class-text {
  margin-bottom: 23px;
}

.class-card {
  --class-gap: 40px;
  margin-bottom: 30px;
}

.class-card .class-content {
  margin-top: -100px;
  background-color: var(--smoke-theme);
  border-radius: 50px;
  padding: 134px 40px 30px 40px;
}

.class-card .class-img {
  max-width: calc(100% - var(--class-gap) * 2);
  margin-left: var(--class-gap);
  position: relative;
}

.class-card .class-img a {
  display: block;
  overflow: hidden;
  border-radius: 50px;
}

.class-card .class-img img {
  border-radius: 50px;
  transition: 0.4s;
}

.class-card .class-fee {
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  border-radius: 50px;
  position: absolute;
  bottom: -30px;
  right: 30px;
  transition: all ease 0.4s;
}

.class-card .class-title {
  color: var(--theme-color);
}

.class-card .class-title:hover {
  color: var(--title-color);
}

.class-card .class-text {
  color: var(--title-color);
}

.class-card .vs-btn {
  padding: 6px 20px 6px 20px;
  min-width: auto;
  font-size: 16px;
  margin-top: 25px;
}

.class-card:hover .class-img img {
  transform: scale(1.15);
}

@media not all and (min-resolution: 0.001dpcm) {
  .class-card:hover .class-img img {
    transform: scale(1);
  }
}

.class-card:hover .class-fee {
  background-color: var(--secondary-color);
}

.class-card:hover .class-title a {
  color: var(--secondary-color);
}

.class-card:hover .class-info li span {
  color: var(--secondary-color);
}

.class-card:hover .vs-btn {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.class-card:hover .vs-btn:hover {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}

.class-img {
  display: block;
}

.class-img img {
  transition: 0.4s;
}

.class-info {
  display: flex;
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.class-info li {
  padding: 0;
  margin: 0;
  list-style: none;
  flex: auto;
  margin-right: 24px;
  border-right: 1px solid rgb(246, 209, 201);
}

.class-info li p,
.class-info li span {
  display: block;
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
}

.class-info li p {
  color: var(--title-color);
  margin-bottom: 6px;
  margin-top: -2px;
}

.class-info li span {
  color: var(--theme-color);
  transition: all ease 0.4s;
}

.class-info li:last-child {
  border-right: none;
  margin-right: 0 !important;
}

.class-box {
  border-radius: 30px;
  background-color: var(--white-color);
  box-shadow: 0px 13px 20px 0px rgba(229, 226, 219, 0.4);
  padding: 40px;
  margin-bottom: 30px;
}

.class-box .class-img {
  border-radius: 30px;
  overflow: hidden;
  margin-bottom: 30px;
}

.class-box .class-img img {
  border-radius: 30px;
}

.class-box .class-profile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 23px;
  padding-bottom: 23px;
  margin-bottom: 23px;
  border-top: 1px solid;
  border-bottom: 1px solid;
  border-color: rgb(241, 241, 241);
}

.class-box .class-profile .avator {
  width: 50px;
  height: 50px;
  overflow: hidden;
  margin-right: 12px;
  border-radius: 200px;
}

.class-box .class-profile .avator img {
  transform: scale(1);
  transition: all ease 0.4s;
  border-radius: 200px;
}

.class-box .class-profile .info {
  line-height: 1;
}

.class-box .class-profile .author-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

.class-box .class-profile .author-desig {
  font-size: 14px;
}

.class-box .class-profile:hover .avator img {
  transform: scale(1.15);
}

@media not all and (min-resolution: 0.001dpcm) {
  .class-box .class-profile:hover .avator img {
    transform: scale(1);
  }
}

.class-box .profile {
  display: flex;
  align-items: center;
}

.class-box .class-fee {
  background-color: var(--smoke-theme);
  color: var(--theme-color);
  padding: 7px;
  border-radius: 10px;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  min-width: 84px;
  transition: all ease 0.4s;
}

.class-box .class-fee:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.class-box .class-title {
  color: var(--title-color);
}

.class-box .class-title:hover {
  color: var(--theme-color);
}

.class-box .class-info {
  justify-content: space-between;
}

.class-box .class-info li {
  flex: 1;
  border-right: 1px solid rgb(241, 241, 241);
}

.class-box .class-info li:last-child {
  border-right: none;
  margin-left: auto;
}

.class-box .vs-btn {
  padding: 6px 20px 6px 20px;
  min-width: auto;
  font-size: 16px;
  margin-top: 25px;
}

.class-box .vs-btn:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.class-box:hover .class-img img {
  transform: scale(1.15);
}

@media not all and (min-resolution: 0.001dpcm) {
  .class-box:hover .class-img img {
    transform: scale(1);
  }
}

.class-title a {
  color: inherit;
}

.class-title:hover a {
  color: inherit;
}

.classes-section-two {
  padding-bottom: 90px;
}

.classes-section-two .slick-prev {
  margin-right: 0;
  margin-left: 65px;
}

.classes-section-two .slick-next {
  margin-left: 0;
  margin-right: 65px;
}

.classes-section-two .title-area {
  margin-bottom: 13px;
}

.classes-section-two .title-area .sec-title {
  /* Extra small devices */
  /* Extra small devices */
}

@media (max-width: 575px) {
  .classes-section-two .title-area .sec-title {
    font-size: 31px;
  }
}

@media (max-width: 416px) {
  .classes-section-two .title-area .sec-title {
    font-size: 25px;
  }
}

.classes-section-two .bloon {
  position: absolute;
  top: 65px;
  left: 130px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .classes-section-two .bloon {
    display: none !important;
  }
}

.classes-section-two .bnsnan {
  position: absolute;
  top: 140px;
  right: 110px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .classes-section-two .bnsnan {
    display: none !important;
  }
}

.classes-section-two .girl {
  position: absolute;
  bottom: 50px;
  left: 80px;
  /* Medium Large devices */
}

@media (max-width: 1399px) {
  .classes-section-two .girl {
    display: none !important;
  }
}

.class-card-two {
  --class-gap: 40px;
  margin: 30px 0;
  padding: 28px 28px 25px;
  border-radius: 30px;
  border: 1px solid transparent;
  background: var(--white-color);
  box-shadow: 0px 2px 25px 0px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  /* Medium devices */
}

@media (max-width: 991px) {
  .class-card-two {
    padding: 25px 17px 35px;
  }
}

.class-card-two .class-content {
  padding-top: 30px;
}

.class-card-two .auther-info {
  border-bottom: 1px solid #F1F1F1;
  padding-bottom: 25px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.class-card-two .auther-info .auther-content {
  position: relative;
  padding: 2px 0 0 61px;
}

.class-card-two .auther-info .auther-content img {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50px;
}

.class-card-two .auther-info .auther-content .name {
  font-size: 18px;
  line-height: 28px;
  margin-bottom: -6px;
}

.class-card-two .auther-info .auther-content .designation {
  font-size: 14px;
  display: block;
}

.class-card-two .auther-info .price-btn {
  font-size: 18px;
  font-weight: 500;
  border-radius: 10px;
  background-color: var(--theme-color);
  color: #fff;
  padding: 7px 17px;
  display: inline-block;
}

.class-card-two .class-img {
  position: relative;
}

.class-card-two .class-img a {
  display: block;
  overflow: hidden;
  border-radius: 30px;
}

.class-card-two .class-img img {
  border-radius: 30px;
  transition: 0.4s;
  width: 100%;
}

.class-card-two .class-title {
  color: var(--theme-color);
}

.class-card-two .class-title:hover {
  color: var(--title-color);
}

.class-card-two .class-text {
  color: var(--title-color);
  border-bottom: 1px solid #F1F1F1;
  padding-bottom: 21px;
  margin-bottom: 25px;
}

.class-card-two .vs-btn {
  padding: 6px 20px 6px 20px;
  min-width: auto;
  font-size: 16px;
  margin-top: 25px;
}

.class-card-two:hover {
  border-color: var(--secondary-color);
  box-shadow: unset;
}

.class-card-two:hover .class-img img {
  transform: scale(1.15);
}

@media not all and (min-resolution: 0.001dpcm) {
  .class-card-two:hover .class-img img {
    transform: scale(1);
  }
}

.class-card-two:hover .class-fee {
  background-color: var(--secondary-color);
}

.class-card-two:hover .class-title a {
  color: var(--secondary-color);
}

.class-card-two:hover .auther-info .auther-content .name {
  color: var(--theme-color);
}

.class-card-two:hover .class-info li span {
  color: var(--theme-color);
}

.class-card-two:hover .price-btn {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.classes-section-three {
  padding: 81px 0 64px;
  position: relative;
}

.classes-section-three::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(135, 201, 13, 0.8);
  z-index: -11;
}

.classes-section-three::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(16, 55, 65, 0.2);
  z-index: -1;
}

.classes-section-three .title-area .sub-title {
  color: var(--white-color);
}

.classes-section-three .title-area .sec-title {
  margin-bottom: 0;
  color: var(--white-color);
  /* Large devices */
}

@media (max-width: 1199px) {
  .classes-section-three .title-area .sec-title {
    font-size: 40px;
  }
}

.classes-section-three .vs-btn.style-2 {
  background-color: var(--theme-color);
  color: var(--white-color);
  width: 144px;
  padding: 11px 30px;
}

.classes-section-three .grass {
  position: absolute;
  top: -59px;
  right: 69px;
  z-index: -111;
}

/* -------- Class Details ------- */
.class-big-img img {
  border-radius: 30px;
}

.class-thumb-img {
  margin-top: var(--bs-gutter-x);
}

.class-thumb-img img {
  border-radius: 30px;
  width: 100%;
  outline: 2px solid transparent;
  outline-offset: -2px;
  transition: all ease 0.4s;
}

.class-thumb-img img:hover,
.class-thumb-img .slick-current img {
  outline-color: var(--theme-color);
  cursor: pointer;
}

.vs-register-form {
  border: none;
}

/* Large devices */
@media (max-width: 1199px) {
  .class-info li {
    margin-right: 4px;
  }

  .class-box {
    padding: 25px;
  }

  .class-box .class-fee {
    font-size: 16px;
    min-width: 70px;
    padding: 4px 7px 3px 7px;
  }

  .class-box .class-info li {
    margin-right: 10px;
  }

  .classes-section-three .ms-auto {
    margin: 0 auto;
  }

  .classes-section-three .ms-auto .vs-btn {
    margin-top: 20px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .class-card {
    --class-gap: 30px;
  }

  .class-card .class-content {
    padding: 119px 30px 26px 30px;
    border-radius: 30px;
  }

  .class-card .class-img a {
    border-radius: 30px;
  }

  .class-info li {
    margin-right: 10px;
  }

  .class-box .class-info li {
    margin-right: 20px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .class-info li {
    border-right: none;
  }

  .class-box {
    padding: 40px;
  }

  .class-info li {
    margin-right: 10px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .class-card {
    --class-gap: 25px;
  }

  .class-card .class-content {
    padding: 119px 25px 36px 25px;
  }

  .class-box {
    padding: 25px;
  }

  .class-box .class-info li {
    border-right: none;
  }

  .classes-section-three .title-area .sec-title {
    font-size: 26px;
    line-height: 36px;
  }
}

/*------------------- 4.23. Process  -------------------*/
.process-card {
  border-radius: 40px;
  padding: 40px 40px 32px 40px;
  position: relative;
  background-color: var(--white-color);
  transition: all ease 0.4s;
}

.process-card .process-number {
  width: 60px;
  height: 60px;
  line-height: 60px;
  background-color: #dbeaf7;
  color: var(--theme-color);
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  border-radius: 50px;
  position: absolute;
  top: 35px;
  right: -30px;
}

.process-card .process-direction {
  position: absolute;
  left: calc(100% + 30px);
  top: 42px;
}

.process-card .process-head {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.process-card .process-icon {
  width: 50px;
  margin-right: 20px;
}

.process-card .process-icon img {
  transition: all ease 0.4s;
  filter: none;
}

.process-card .process-title {
  max-width: 130px;
  margin-bottom: 0;
  transition: all ease 0.4s;
}

.process-card .process-text {
  margin-bottom: 0;
  transition: all ease 0.4s;
}

.process-card:hover {
  background-color: var(--secondary-color);
}

.process-card:hover .process-icon img {
  filter: brightness(0) invert(1);
}

.process-card:hover .process-title,
.process-card:hover .process-text {
  color: var(--white-color);
}

.process-area {
  position: relative;
}

.process-area .process-line {
  width: calc(100% + 80px);
  position: absolute;
  top: 44%;
  left: -30px;
  z-index: -1;
}

.process-box-body {
  width: 200px;
  height: 210px;
  box-shadow: 0px 20px 60px 0px rgba(16, 55, 65, 0.06);
  background-color: var(--white-color);
  text-align: center;
  margin-left: 24px;
  border-radius: 50% 50% 50% 50%;
  position: relative;
}

.process-box-body:before {
  content: "";
  width: calc(100% + 8px);
  height: calc(100% + 8px);
  border: 4px solid rgb(242, 244, 249);
  position: absolute;
  top: -3px;
  left: -3px;
  border-radius: 50% 50% 50% 50%;
  z-index: 0;
  transition: all ease 0.4s;
}

.process-box-body .process-number {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  color: var(--white-color);
  background-color: var(--theme-color);
  display: block;
  font-size: 24px;
  font-weight: 500;
  text-align: center;
  transform: translate(-24px, -24px);
  position: relative;
}

.process-box-body .process-number:before,
.process-box-body .process-number:after {
  content: "";
  width: calc(100% + 40px);
  height: calc(100% + 40px);
  background-color: var(--theme-color);
  position: absolute;
  top: -20px;
  left: -20px;
  border-radius: 50%;
  box-shadow: 0px 20px 60px 0px rgba(0, 35, 160, 0.06);
  opacity: 0.6;
  z-index: -1;
  animation: numPulse 4s infinite;
}

.process-box-body .process-number:after {
  animation-delay: 2s;
}

.process-box-body .process-content {
  padding: 60px 23px 0px 23px;
}

.process-box-body .process-icon {
  margin-bottom: 12px;
  display: block;
}

.process-box-body .process-name {
  font-weight: 600;
  color: var(--title-color);
}

.process-box-body .process-name a {
  color: inherit;
}

.process-box-body .process-name a:hover {
  color: var(--theme-color);
}

.process-box-body:hover:before {
  border-color: #9c29b2;
}

.process-box:nth-child(even) .process-box-body {
  margin-top: 40px;
}

@keyframes numPulse {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .process-card .process-number {
    right: -10px;
  }

  .process-card .process-direction {
    display: none;
  }

  .process-box-body {
    width: 187px;
  }

  .process-box-body .process-content {
    padding: 0 18px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .process-card .process-number:after {
    display: none;
  }

  .process-box-body {
    width: 270px;
  }

  .process-box-body .process-content {
    padding: 0 25px;
  }

  .process-box-body.mt-40 {
    margin-top: 0;
  }

  .process-area .process-line {
    display: none;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .process-title {
    max-width: 116px;
  }

  .process-box-body {
    width: 200px;
  }

  .process-box-body .process-content {
    padding: 0 30px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .process-card {
    border-radius: 30px;
  }

  .process-card .process-number {
    width: 45px;
    height: 45px;
    line-height: 45px;
    background-color: #dbeaf7;
    font-size: 20px;
    top: 15px;
    right: -5px;
  }

  .process-box-body {
    max-width: 280px;
  }

  .process-area .row.justify-content-between {
    justify-content: center !important;
  }
}

/*------------------- 4.24. Appointment  -------------------*/
.appointment-form {
  padding: 60px;
  border-radius: 50px;
  background-color: var(--smoke-theme);
}

.appointment-form select,
.appointment-form .form-select,
.appointment-form .form-control {
  height: 60px;
  border: none;
  border-radius: 30px;
}

.appointment-form .vs-btn {
  width: 100%;
  display: block;
  height: 60px;
}

.appointment-form textarea {
  resize: none;
}

/* Large devices */
@media (max-width: 1199px) {
  .appointment-form {
    padding: 40px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .appointment-form {
    padding: 40px 20px;
    border-radius: 30px;
  }

  .appointment-form select,
  .appointment-form .form-select,
  .appointment-form .form-control {
    height: 45px;
    border-radius: 23px;
  }

  .appointment-form .vs-btn {
    height: 45px;
  }

  .appointment-form textarea {
    resize: none;
    padding-top: 7px;
  }
}

/*------------------- 4.25. Team  -------------------*/
.team-card {
  --gap: 40px;
  text-align: left;
  position: relative;
  margin-bottom: 30px;
}

.team-card .team-img {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  max-width: calc(100% - var(--gap) * 2);
  mask-size: contain;
  mask-repeat: no-repeat;
  z-index: 2;
}

.team-card .team-img:before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: var(--title-color);
  top: 0;
  left: 0;
  z-index: 0;
  visibility: hidden;
  opacity: 0;
  transition: 0.4s ease-in-out;
}

.team-card .team-img img {
  width: 100%;
}

.team-card .team-content {
  width: 100%;
  height: 100%;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 30px;
}

.team-card .team-text {
  visibility: hidden;
  opacity: 0;
  transform: translateY(-60px);
  transition: 0.4s ease-in-out;
  color: var(--white-color);
}

.team-card .team-info {
  border-radius: 30px;
  box-shadow: 0px 13px 16px 0px rgba(226, 222, 208, 0.07);
  background-color: #fff;
  text-align: center;
  padding: 180px 40px 35px 40px;
  margin-top: -155px;
}

.team-card .team-title {
  color: var(--title-color);
  margin-bottom: 0;
}

.team-card .team-title a {
  color: inherit;
}

.team-card .team-title a:hover {
  color: var(--theme-color);
}

.team-card .expand-btn {
  box-shadow: 0px 10px 15px 0px rgba(156, 41, 178, 0.25);
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.team-card .multi-social {
  --icon-size: 40px;
}

.team-card .multi-social a {
  visibility: hidden;
  opacity: 0;
  transform: translateY(60px);
  transition: 0.4s ease-in-out;
}

.team-card .multi-social a i {
  margin-right: 4px;
  background-color: var(--white-color);
  color: var(--theme-color);
}

.team-card .multi-social a:nth-child(1) {
  transition-delay: 0s;
}

.team-card .multi-social a:nth-child(2) {
  transition-delay: 0.1s;
}

.team-card .multi-social a:nth-child(3) {
  transition-delay: 0.2s;
}

.team-card .multi-social a:nth-child(4) {
  transition-delay: 0.3s;
}

.team-card .multi-social a:hover i {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.team-card:hover .team-img:before {
  visibility: visible;
  opacity: 0.8;
}

.team-card:hover .team-text {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.team-card:hover .multi-social a {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.team-card:hover .expand-btn .fa-share-alt:before {
  content: "\f062";
  font-weight: 300;
}

.team-box {
  position: relative;
  margin-bottom: 30px;
}

.team-box .team-img {
  border-radius: 30px;
  overflow: hidden;
}

.team-box .team-img img {
  transition: all ease 0.4s;
  transform: scale(1.002);
  border-radius: 30px;
}

.team-box .multi-social {
  width: var(--icon-size);
  position: absolute;
  top: 40px;
  right: 40px;
}

.team-box .multi-social a {
  visibility: hidden;
  opacity: 0;
  transform: translateX(40px);
  transition: 0.4s ease-in-out;
}

.team-box .multi-social a:nth-child(1) {
  transition-delay: 0s;
}

.team-box .multi-social a:nth-child(2) {
  transition-delay: 0.1s;
}

.team-box .multi-social a:nth-child(3) {
  transition-delay: 0.2s;
}

.team-box .multi-social a:nth-child(4) {
  transition-delay: 0.3s;
}

.team-box .multi-social a i {
  margin-bottom: 10px;
}

.team-box .team-info {
  max-width: 260px;
  border-radius: 30px;
  background-color: var(--white-color);
  box-shadow: 0px 15px 30px 0px rgba(37, 69, 116, 0.045);
  text-align: center;
  margin: 0 auto;
  padding: 25px 10px 20px 10px;
  margin-top: -46px;
  z-index: 2;
  position: relative;
}

.team-box .team-title {
  font-weight: 600;
  margin-bottom: 0;
  line-height: 1;
}

.team-box .team-title a {
  color: inherit;
}

.team-box .team-title a:hover {
  color: var(--theme-color);
}

.team-box:hover .team-img img {
  transform: scale(1.07);
}

@media not all and (min-resolution: 0.001dpcm) {
  .team-box:hover .team-img img {
    transform: scale(1);
  }
}

.team-box:hover .multi-social a {
  visibility: visible;
  opacity: 1;
  transform: translateX(0);
}

/* ------- Team Details */
.team-details {
  position: relative;
}

.team-details .team-content-card {
  position: absolute;
  bottom: 0;
}

.team-details .team-img {
  position: relative;
  z-index: 2;
  max-width: 490px;
}

.team-content-card {
  --border-gap: 30px;
  padding: 75px 80px 75px 270px;
  border-radius: 100px;
  margin: var(--border-gap);
  position: relative;
}

.team-content-card:after {
  content: "";
  width: calc(100% + var(--border-gap) * 2);
  height: calc(100% + var(--border-gap) * 2);
  position: absolute;
  top: calc(0px - var(--border-gap));
  left: calc(0px - var(--border-gap));
  border: 2px dashed var(--theme-color);
  border-radius: inherit;
  z-index: -1;
}

.team-content-card .team-name {
  font-size: 40px;
  margin-bottom: 0;
}

.team-content-card .team-desig {
  display: block;
  margin-bottom: 17px;
  color: var(--theme-color);
  font-size: 18px;
}

.team-content-card .info-list ul {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

.team-content-card .info-list li {
  color: var(--body-color);
  font-family: var(--body-font);
  font-size: 20px;
  margin-bottom: 8px;
}

.team-content-card .info-list li:last-child {
  margin-bottom: 0;
}

.team-content-card .info-list li a {
  color: inherit;
}

.team-content-card .info-list li a:hover {
  color: var(--theme-color);
}

.team-content-card .info-list strong {
  color: var(--title-color);
  font-weight: 600;
  margin-right: 10px;
}

.team-section-two {
  position: relative;
}

.team-section-two .slick-arrow.slick-next {
  margin-left: 0;
  margin-right: 64px;
}

.team-section-two .slick-arrow.slick-prev {
  margin-right: 0;
  margin-left: 64px;
}

.team-section-two .scale {
  position: absolute;
  top: 212px;
  left: 140px;
  /* Extra large devices */
}

@media (max-width: 1680px) {
  .team-section-two .scale {
    display: none !important;
  }
}

.team-section-two .kids {
  position: absolute;
  bottom: 30px;
  right: 100px;
  /* Extra large devices */
}

@media (max-width: 1680px) {
  .team-section-two .kids {
    display: none !important;
  }
}

.team-card-two {
  position: relative;
  margin-bottom: 40px;
  border-radius: 20px;
  background: #FFF;
  box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.07);
}

.team-card-two:hover .image img {
  transform: scale(1.1);
}

.team-card-two:hover .social-links {
  transform: scaleY(1);
  opacity: 1;
  visibility: visible;
}

.team-card-two .team-img {
  overflow: hidden;
  position: relative;
  margin-bottom: 0;
  border-radius: 20px 20px 0 0;
  z-index: 1;
}

.team-card-two .team-img .image img {
  width: 100%;
  transition: all 400ms ease;
}

.team-card-two .team-info {
  position: relative;
  padding: 50px 30px 31px;
  text-align: center;
  z-index: 2;
}

.team-card-two .team-info .name {
  margin-bottom: 6px;
  color: var(--title-color);
  font-weight: 700;
}

.team-card-two .team-info .name a {
  color: inherit;
}

.team-card-two .team-info .name:hover {
  color: var(--theme-color);
}

.team-card-two .team-info .designation {
  position: relative;
  display: block;
  transition: all 400ms ease;
}

.team-card-two .share-icon {
  position: absolute;
  top: -25px;
  right: 30px;
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
  color: #ffffff;
  background-color: var(--theme-color);
  transition: all 300ms ease;
  border-radius: 50%;
  z-index: 3;
}

.team-card-two .social-links {
  position: absolute;
  right: 30px;
  bottom: 123%;
  display: flex;
  align-items: center;
  flex-direction: column;
  transform: scaleY(0);
  transform-origin: bottom;
  z-index: 3;
  opacity: 0;
  visibility: hidden;
  transition: all 400ms ease;
  background-color: var(--theme-color);
  border-radius: 50px;
  padding: 26px 0 23px;
}

.team-card-two .social-links a {
  position: relative;
  width: 50px;
  text-align: center;
  margin-top: 9px;
  font-size: 16px;
  color: #ffffff;
  background-color: var(--bg-theme-color1);
  border-radius: 10px;
  transition: all 300ms ease;
  display: inline-block;
}

.team-card-two .social-links a:first-child {
  margin-top: 0;
}

.team-card-two .social-links a:hover {
  color: var(--secondary-color);
}

/* Large devices */
@media (max-width: 1199px) {
  .team-card {
    --gap: 25px;
  }

  .team-details .team-img {
    max-width: 450px;
  }

  .team-content-card {
    --border-gap: 20px;
    padding: 30px 30px 30px 270px;
    border-radius: 50px;
  }

  .team-content-card .team-name {
    font-size: 30px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .team-details .team-content-card {
    position: relative;
    bottom: unset;
    z-index: 3;
  }

  .team-content-card {
    padding: 30px 40px;
  }

  .team-content-card .team-name {
    font-size: 30px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .team-card {
    --gap: 40px;
  }

  .team-content-card {
    --border-gap: 15px;
    padding: 20px 25px;
    border-radius: 30px;
  }

  .team-content-card .info-list li {
    font-size: 18px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .team-content-card .team-name {
    font-size: 26px;
  }

  .team-content-card .info-list li {
    font-size: 16px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .team-card {
    --gap: 25px;
  }

  .team-card .team-info {
    padding: 165px 40px 20px 40px;
  }

  .team-card .expand-btn {
    bottom: 90px;
  }

  .team-box .multi-social {
    top: 25px;
    right: 25px;
  }
}

@media (max-width: 350px) {
  .team-card .team-content {
    padding: 10px;
  }

  .team-card .team-text {
    font-size: 14px;
    line-height: 1.4;
  }
}

/*------------------- 4.26. Project Gallery  -------------------*/
.project-card {
  position: relative;
  border-radius: 30px;
  overflow: hidden;
}

.project-card .project-img {
  position: relative;
}

.project-card .project-img img {
  transform: scale(1.001);
  transition: all ease 0.6s;
  border-radius: 30px;
}

.project-card .project-img:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: var(--secondary-color);
  transition: 0.6s ease-in-out;
  transform: scale(0);
  border-radius: 30px;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}

.project-card .plus-btn {
  font-size: 72px;
  color: var(--white-color);
  position: absolute;
  top: 50%;
  left: 50%;
  visibility: hidden;
  opacity: 0;
  transition: 0.3s;
  z-index: 2;
  transition-delay: 0.3s;
  transform: translate(-50%, -50%) scale(0);
}

.project-card .plus-btn:hover {
  color: var(--theme-color);
}

.project-card:hover .project-img img {
  transform: scale(1.07);
}

@media not all and (min-resolution: 0.001dpcm) {
  .project-card:hover .project-img img {
    transform: scale(1);
  }
}

.project-card:hover .project-img:before {
  transform: scale(1);
  opacity: 0.75;
  visibility: visible;
  transform: scale(1);
}

.project-card:hover .plus-btn {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.project-menu button {
  border: none;
  background-color: transparent;
  font-size: 18px;
  font-weight: 600;
  color: var(--title-color);
  min-width: 180px;
  height: 74px;
  padding: 20px 50px;
  margin: 0 10px;
  transition: 0.3s ease-in-out;
  position: relative;
  z-index: 2;
}

.project-menu button img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  transition: 0.3s;
}

.project-menu button .btn-active-bg {
  visibility: hidden;
  opacity: 0;
  z-index: -1;
  transition: 0.3s;
}

.project-menu button:hover .btn-active-bg,
.project-menu button.active .btn-active-bg {
  visibility: visible;
  opacity: 1;
}

.project-section-two .paret {
  position: absolute;
  top: 130px;
  left: 0;
  /* Large devices */
}

@media (max-width: 1199px) {
  .project-section-two .paret {
    display: none !important;
  }
}

.project-section-two .flayer {
  position: absolute;
  top: 215px;
  right: 103px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .project-section-two .flayer {
    display: none !important;
  }
}

.project-section-two .rocket {
  position: absolute;
  bottom: 60px;
  right: 130px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .project-section-two .rocket {
    display: none !important;
  }
}

.project-section-two .tube {
  position: absolute;
  bottom: 106px;
  left: 169px;
  /* Large devices */
}

@media (max-width: 1199px) {
  .project-section-two .tube {
    display: none !important;
  }
}

.project-section-two .project-menu {
  /* Medium devices */
}

@media (max-width: 991px) {
  .project-section-two .project-menu {
    text-align: left !important;
    margin-bottom: 20px;
  }
}

.project-section-two .project-menu button {
  border: none;
  background-color: var(--white-color);
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-color);
  padding: 0px 30px;
  min-width: unset;
  margin: 0 10px;
  height: 50px;
  line-height: 50px;
  transition: 0.3s ease-in-out;
  position: relative;
  border-radius: 50px;
  z-index: 2;
  /* Medium devices */
}

.project-section-two .project-menu button:hover,
.project-section-two .project-menu button.active {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.project-section-two .project-menu button img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  transition: 0.3s;
}

.project-section-two .project-menu button .btn-active-bg {
  visibility: hidden;
  opacity: 0;
  z-index: -1;
  transition: 0.3s;
}

@media (max-width: 991px) {
  .project-section-two .project-menu button {
    margin-bottom: 20px;
  }
}

.project-card-two {
  position: relative;
  border-radius: 30px;
  overflow: hidden;
}

.project-card-two .project-img {
  position: relative;
}

.project-card-two .project-img img {
  transform: scale(1.001);
  transition: all ease 0.6s;
  border-radius: 30px;
  width: 100%;
}

.project-card-two .project-img:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: var(--secondary-color);
  transition: 0.6s ease-in-out;
  transform: scale(0);
  border-radius: 30px;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}

.project-card-two .plus-btn {
  font-size: 24px;
  width: 60px;
  height: 60px;
  line-height: 65px;
  border-radius: 10px;
  text-align: center;
  background-color: var(--white-color);
  color: var(--secondary-color);
  position: absolute;
  top: 50%;
  left: 50%;
  visibility: hidden;
  opacity: 0;
  transition: 0.3s;
  z-index: 2;
  transition-delay: 0.3s;
  transform: translate(-50%, -50%) scale(0);
}

.project-card-two .plus-btn:hover {
  color: var(--theme-color);
}

.project-card-two:hover .project-img img {
  transform: scale(1.07);
}

@media not all and (min-resolution: 0.001dpcm) {
  .project-card-two:hover .project-img img {
    transform: scale(1);
  }
}

.project-card-two:hover .project-img:before {
  transform: scale(1);
  opacity: 0.75;
  visibility: visible;
  transform: scale(1);
}

.project-card-two:hover .plus-btn {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Medium devices */
@media (max-width: 991px) {
  .project-menu button {
    font-size: 16px;
    min-width: 150px;
    height: 60px;
    padding: 18px 30px;
    margin: 0 4px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .project-menu button {
    margin: 6px 4px;
  }
}

/*------------------- 4.27. Testimonial  -------------------*/
.testi-box {
  border-radius: 30px;
  background-color: var(--white-color);
  box-shadow: 0px 7px 12px 4px rgba(75, 83, 100, 0.06);
  padding: 60px 40px;
  text-align: center;
  margin-top: 5px;
  margin-bottom: 25px;
}

.testi-box .testi-avater {
  width: 103px;
  height: 103px;
  position: relative;
  margin: 0 auto;
  margin-bottom: 20px;
}

.testi-box .testi-avater i {
  color: var(--theme-color);
  font-size: 30px;
  width: 60px;
  height: 60px;
  line-height: 60px;
  background-color: var(--white-color);
  border-radius: 50%;
  position: absolute;
  top: 35%;
  left: -38px;
}

.testi-box .testi-avater img {
  border-radius: 50%;
}

.testi-box .testi-title {
  font-weight: 600;
}

.testi-box .name {
  margin-bottom: 0;
  line-height: 1;
  font-weight: 600;
}

.testi-slider-area {
  position: relative;
}

.vs-icon-box {
  --icon-size: 50px;
}

.vs-icon-box.testi-1 .icon-btn {
  background-color: var(--white-color);
  border: 1px solid var(--theme-color);
}

.vs-icon-box.testi-1 .icon-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.vs-icon-box.testi-1,
.vs-icon-box.testi-2 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -90px;
  width: var(--icon-size);
}

.vs-icon-box.testi-2 {
  top: 35%;
  transform: translateY(-35%);
}

.vs-icon-box.testi-2 .icon-btn {
  background-color: var(--white-color);
}

.vs-icon-box.testi-2 .icon-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.rating-box {
  border-radius: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0px 20px 42.75px 2.25px rgba(214, 206, 185, 0.15);
  background-color: var(--white-color);
  padding: 30px 30px;
  max-width: 410px;
  margin-left: auto;
  margin-right: auto;
}

.rating-box .number {
  margin-bottom: 0;
  border-right: 2px solid #f7f3e8;
  color: var(--theme-color);
  line-height: 0.9;
  padding-top: 5px;
  padding-right: 20px;
  margin-right: 20px;
}

.rating-box .rating-count {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 0;
}

.rating-box .star-rating {
  margin-bottom: 10px;
}

.testi-grid-wrapper {
  position: relative;
}

#clipShape {
  position: absolute;
}

.testi-grid {
  --icon-size: 80px;
  margin-top: calc(var(--icon-size) / 2);
}

.testi-grid .testi-text {
  background-color: var(--white-color);
  padding: 55px 40px 35px 40px;
  border-radius: 30px;
  margin-bottom: 20px;
  position: relative;
}

.testi-grid .testi-text i {
  font-size: 36px;
  position: absolute;
  color: var(--theme-color);
  top: calc(var(--icon-size) / -2);
  left: calc(var(--icon-size) / 2);
  width: var(--icon-size);
  height: var(--icon-size);
  line-height: var(--icon-size);
  background-color: var(--white-color);
  text-align: center;
  border-radius: 50%;
}

.testi-grid .testi-text:after {
  content: "";
  height: 38px;
  width: 58px;
  background-color: inherit;
  clip-path: url(#testiAfterShape);
  position: absolute;
  top: calc(100% - 1px);
  right: calc(var(--icon-size) / 2);
}

.testi-grid .testi-text p:last-child {
  margin-bottom: 0;
}

.testi-grid .testi-author {
  display: flex;
  align-items: center;
}

.testi-grid .name {
  line-height: 1;
  margin-bottom: 0;
}

.testi-grid .avater {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.testi-grid .avater img {
  border-radius: 50%;
}

/* Large devices */
@media (max-width: 1199px) {
  .testi-box {
    padding: 30px 30px 25px 30px;
  }

  .testi-grid {
    --icon-size: 50px;
  }

  .testi-grid .testi-text {
    padding: 40px 25px 20px 25px;
  }

  .testi-grid .testi-text i {
    font-size: 24px;
  }

  .testi-grid .quote-icon img {
    width: 23px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .testi-grid-wrapper.ml-40 {
    margin-left: 0;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .testi-grid .testi-text {
    padding: 40px 35px 30px 35px;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .testi-grid .testi-text {
    padding: 40px 25px 20px 25px;
  }

  .rating-box {
    padding: 25px 25px;
  }
}

/*------------------- 4.28. Choose  -------------------*/
.choose-tab {
  --border-gap: 4px;
  border: 2px dashed var(--theme-color);
  border-radius: 50px;
  margin-bottom: 30px;
  height: 62px;
  padding: var(--border-gap);
  display: inline-block;
  position: relative;
  z-index: 2;
  /* Extra small devices */
}

@media (max-width: 575px) {
  .choose-tab {
    --border-gap: unset;
  }
}

.choose-tab .indicator {
  position: absolute;
  top: var(--border-gap) !important;
  left: 0;
  background-color: var(--theme-color);
  border-radius: 50px;
  z-index: -1;
  transition: 0.4s;
  /* Extra small devices */
}

@media (max-width: 575px) {
  .choose-tab .indicator {
    display: none;
  }
}

.choose-content .tab-pane p:last-child {
  margin-bottom: 0;
}

.choose-btn {
  border: none;
  color: var(--theme-color);
  background-color: transparent;
  font-size: 18px;
  font-weight: 600;
  border-radius: 50px;
  padding: 12px 30px;
  text-align: center;
  position: relative;
}

.choose-btn:hover {
  color: var(--secondary-color);
}

.choose-btn.active {
  color: var(--white-color);
}

.long-img {
  height: 100%;
}

.long-img img {
  height: 100%;
  object-fit: cover;
  object-position: center center;
}

.long-img,
.short-img {
  border-radius: 30px;
  overflow: hidden;
}

.long-img img,
.short-img img {
  border-radius: 30px;
  transition: 0.4s ease-in-out;
}

.long-img:hover img,
.short-img:hover img {
  border-radius: 30px;
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {

  .long-img:hover img,
  .short-img:hover img {
    transform: scale(1);
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .choose-btn {
    padding: 12px 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .why-images {
    --bs-gutter-x: 20px;
  }

  .why-images .mb-30 {
    margin-bottom: 20px;
  }

  .choose-btn {
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 400;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .choose-btn {
    padding: 6px 10px;
    font-size: 14px;
  }
}

/*------------------- 4.29. Event  -------------------*/
.event-card {
  border-radius: 20px;
  box-shadow: 0px 8px 38px 0px rgba(198, 186, 162, 0.15);
  display: flex;
  align-items: center;
  padding: 40px 50px;
  background-color: var(--white-color);
}

.event-card .event-date {
  margin-bottom: 0;
  font-size: 40px;
  padding-right: 35px;
  color: var(--theme-color);
}

.event-card .event-date a:hover {
  color: var(--secondary-color);
}

.event-card .event-content {
  border-left: 1px solid rgb(237, 234, 228);
  padding-left: 35px;
}

.event-card .event-title {
  margin-top: -5px;
  margin-bottom: 10px;
}

.event-title {
  color: var(--title-color);
}

.event-title a {
  color: inherit;
}

.event-title a:hover {
  color: var(--theme-color);
}

.event-info {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.event-info li {
  font-family: var(--body-font);
  display: flex;
}

.event-info li i {
  width: 24px;
  color: var(--theme-color);
  text-align: center;
  margin-right: 5px;
}

.event-more {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0;
  text-align: center;
}

.event-more .link-btn {
  color: var(--title-color);
  position: relative;
}

.event-more .link-btn:after,
.event-more .link-btn:before {
  content: "";
  height: 1px;
  width: 100%;
  background-color: var(--title-color);
  position: absolute;
  bottom: 5px;
  left: 0;
  transition: 0.4s ease-in-out;
}

.event-more .link-btn:before {
  width: 0;
  background-color: var(--theme-color);
  z-index: 2;
}

.event-more .link-btn:hover {
  color: var(--theme-color);
}

.event-more .link-btn:hover:before {
  width: 100%;
}

.event-box {
  --icon-size: 80px;
  position: relative;
}

.event-box .event-img {
  position: relative;
  border-radius: 30px 30px 0 0;
  overflow: hidden;
}

.event-box .event-img img {
  transition: 0.4s;
  border-radius: 30px 30px 0 0;
}

.event-box .event-img:hover img {
  transform: scale(1.1);
}

@media not all and (min-resolution: 0.001dpcm) {
  .event-box .event-img:hover img {
    transform: scale(1);
  }
}

.event-box .event-date {
  font-size: 18px;
  font-weight: 700;
  padding: 11px;
  text-align: center;
  line-height: 20px;
  background-color: var(--theme-color);
  color: var(--white-color);
  width: var(--icon-size);
  height: var(--icon-size);
  border-radius: 50%;
  border: 9px solid var(--smoke-color);
  position: absolute;
  top: calc(var(--icon-size) / -2);
  left: calc(var(--icon-size) / 2);
}

.event-box .event-info li:last-child {
  margin-bottom: 10px;
}

.event-box .event-content {
  background-color: var(--white-color);
  border-radius: 0 0 30px 30px;
  padding: 32px 40px 31px 40px;
  box-shadow: 0px 15px 30px 0px rgba(228, 215, 178, 0.08);
}

.event-box .link-btn {
  text-transform: capitalize;
}

/* ------- Event Details -------*/
.event-slider-area {
  position: relative;
  --icon-gap: 40px;
}

.event-slider-area .arrow-left,
.event-slider-area .arrow-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.event-slider-area .arrow-left {
  left: var(--icon-gap);
}

.event-slider-area .arrow-right {
  right: var(--icon-gap);
}

.event-location {
  height: 100%;
}

.event-location iframe {
  width: 100%;
  height: 100%;
  border-radius: 30px;
}

.event-title-box {
  display: flex;
  align-items: center;
}

.event-title-box .event-title {
  margin-bottom: 0;
}

.event-title-box .date {
  height: 90px;
  width: 80px;
  border-radius: 10px;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  margin-right: 25px;
}

.event-title-box .date .day {
  font-size: 30px;
  width: 100%;
  display: inline-block;
  height: 55px;
  line-height: 55px;
  border-bottom: 2px solid var(--smoke-color);
}

.event-title-box .date .month {
  line-height: 34px;
  text-transform: uppercase;
}

.event-details {
  margin-bottom: 20px;
}

.event-info-list {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

.event-info-list li {
  display: flex;
  margin-bottom: 10px;
}

.event-info-list li:last-child {
  margin-bottom: 0;
}

.event-info-list li i {
  color: var(--theme-color);
  width: 27px;
}

.event-nav {
  padding-top: 17px;
  display: flex;
  justify-content: space-between;
}

.event-nav .nav-btn {
  position: relative;
  font-size: 24px;
  font-weight: 600;
  color: var(--title-color);
}

.event-nav .nav-btn:before {
  content: "";
  height: 2px;
  width: 0;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 2px;
  right: 0;
  transition: 0.3s ease-in-out;
}

.event-nav .nav-btn:hover {
  color: var(--theme-color);
}

.event-nav .nav-btn:hover:before {
  width: 100%;
}

.event-nav .nav-btn:first-child i {
  margin-right: 5px;
}

.event-nav .nav-btn:last-child i {
  margin-left: 5px;
}

.event-nav .nav-btn:last-child:before {
  right: unset;
  left: 0;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .event-box .event-content {
    padding: 22px 25px 21px 25px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .event-card {
    flex-direction: column;
  }

  .event-card .event-date {
    font-size: 36px;
    padding-right: 0;
    margin-bottom: 15px;
  }

  .event-card .event-content {
    border-left: none;
    padding-left: 0;
  }

  .event-card .event-title {
    text-align: center;
  }

  .event-title-box .event-title {
    font-size: 36px;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .event-card {
    padding: 40px 25px;
  }

  .event-box .event-content {
    padding: 32px 40px 31px 40px;
  }

  .event-title-box {
    margin-bottom: 30px;
  }

  .event-title-box .event-title {
    font-size: 30px;
  }
}

/* Small devices */
@media (max-width: 767px) {
  .event-title-box .event-title {
    font-size: 26px;
    line-height: 1;
    margin-bottom: 8px;
  }

  .event-nav {
    text-align: center;
    flex-direction: column;
    justify-content: center;
  }

  .event-nav .nav-btn {
    font-size: 20px;
    display: block;
  }

  .event-nav .nav-btn::before {
    display: none;
  }

  .event-nav .nav-btn:first-child {
    margin-bottom: 10px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .event-card .event-date {
    font-size: 30px;
  }

  .event-more .link-btn::after,
  .event-more .link-btn::before {
    display: none;
  }
}

/* Extra small devices */
@media (max-width: 416px) {
  .event-info li {
    margin-bottom: 8px;
  }

  .event-info li:last-child {
    margin-bottom: 0;
  }

  .event-box {
    --icon-size: 60px;
  }

  .event-box .event-content {
    padding: 25px 25px 25px 25px;
  }

  .event-box .event-info li {
    font-size: 14px;
    margin-bottom: 5px;
  }

  .event-box .event-info li:last-child {
    margin-bottom: 10px;
  }

  .event-box .link-btn {
    font-size: 16px;
  }

  .event-box .event-title {
    margin-bottom: 10px;
  }

  .event-box .event-date {
    font-size: 14px;
    font-weight: 600;
    padding: 12px;
    line-height: 14px;
    border-width: 6px;
  }
}

/*------------------- 4.30. Contact  -------------------*/
.contact-form .form-label {
  margin-bottom: 11px;
  color: var(--title-color);
  font-family: var(--para-font);
  font-weight: 600;
}

.contact-form select,
.contact-form .form-select,
.contact-form .form-control {
  height: 50px;
  padding: 15px 27px;
  background-color: var(--smoke-color);
  border-radius: 10px;
  border: none;
  background-position: right 22px center;
}

.contact-form textarea {
  resize: none;
}

.small-title {
  font-weight: 500;
  display: block;
  margin-bottom: 7px;
  margin-top: -0.5rem;
}

.contact-info-box .contact-title,
.contact-form-box .contact-title {
  position: relative;
  padding-bottom: 10px;
}

.contact-info-box .contact-title:before,
.contact-form-box .contact-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 60px;
  height: 2px;
  background-color: var(--theme-color);
  display: inline-block;
}

.contact-info-box {
  margin-left: 30px;
}

.contact-info-box .contact-title {
  margin-bottom: 25px;
}

.contact-form-box,
.contact-info-box {
  border: 2px dashed rgb(235, 235, 235);
  box-shadow: 0px 8px 40px 0px rgba(105, 116, 119, 0.08);
  border-radius: 30px;
  background-color: rgb(253, 253, 253);
  padding: 35px 40px 40px 40px;
}

.contact-form-box .form-group:last-child {
  margin-bottom: 0;
}

.info-media {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.info-media .media-icon {
  width: 75px;
  height: 75px;
  background-color: var(--white-color);
  text-align: center;
  line-height: 54px;
  border-radius: 50%;
  border: 10px solid #fce6df;
  margin-right: 25px;
}

.info-media .info-title {
  font-weight: 600;
  margin-bottom: 6px;
}

.info-media .info-text {
  margin-bottom: 0;
}

.info-media .info-text a {
  color: inherit;
}

.info-media .info-text a:hover {
  color: var(--theme-color);
}

.contact-location {
  line-height: 1px;
  border-radius: 30px;
  overflow: hidden;
}

.contact-location iframe {
  height: 265px;
}

/* Large devices */
@media (max-width: 1199px) {
  .contact-info-box {
    margin-left: 0;
  }
}

/* Small devices */
@media (max-width: 767px) {

  .contact-form-box,
  .contact-info-box {
    padding: 37px 20px 50px 20px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {

  .contact-info-box,
  .contact-location {
    border-radius: 15px;
  }

  .info-media {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-media .media-icon {
    margin-bottom: 15px;
  }
}

/*=================================
    05. Spacing
==================================*/
/*-- Padding Left And Right --*/
.px-5 {
  padding-right: 5px;
  padding-left: 5px;
}

.px-10 {
  padding-right: 10px;
  padding-left: 10px;
}

.px-15 {
  padding-right: 15px;
  padding-left: 15px;
}

.px-20 {
  padding-right: 20px;
  padding-left: 20px;
}

.px-25 {
  padding-right: 25px;
  padding-left: 25px;
}

.px-30 {
  padding-right: 30px;
  padding-left: 30px;
}

.px-35 {
  padding-right: 35px;
  padding-left: 35px;
}

.px-40 {
  padding-right: 40px;
  padding-left: 40px;
}

.px-45 {
  padding-right: 45px;
  padding-left: 45px;
}

.px-50 {
  padding-right: 50px;
  padding-left: 50px;
}

/*-- Padding Top And Bottom --*/
.py-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-25 {
  padding-top: 25px;
  padding-bottom: 25px;
}

.py-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-35 {
  padding-top: 35px;
  padding-bottom: 35px;
}

.py-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.py-45 {
  padding-top: 45px;
  padding-bottom: 45px;
}

.py-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}

/*-- Padding Top --*/
.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

/*-- Padding Bottom --*/
.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

/*-- Padding Left --*/
.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-45 {
  padding-left: 45px;
}

.pl-50 {
  padding-left: 50px;
}

/*-- Padding Right --*/
.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-35 {
  padding-right: 35px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-45 {
  padding-right: 45px;
}

.pr-50 {
  padding-right: 50px;
}

/*-- margin Left And Right --*/
.mx-5 {
  margin-right: 5px;
  margin-left: 5px;
}

.mx-10 {
  margin-right: 10px;
  margin-left: 10px;
}

.mx-15 {
  margin-right: 15px;
  margin-left: 15px;
}

.mx-20 {
  margin-right: 20px;
  margin-left: 20px;
}

.mx-25 {
  margin-right: 25px;
  margin-left: 25px;
}

.mx-30 {
  margin-right: 30px;
  margin-left: 30px;
}

.mx-35 {
  margin-right: 35px;
  margin-left: 35px;
}

.mx-40 {
  margin-right: 40px;
  margin-left: 40px;
}

.mx-45 {
  margin-right: 45px;
  margin-left: 45px;
}

.mx-50 {
  margin-right: 50px;
  margin-left: 50px;
}

/*-- margin Top And Bottom --*/
.my-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.my-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.my-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}

.my-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.my-25 {
  margin-top: 25px;
  margin-bottom: 25px;
}

.my-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}

.my-35 {
  margin-top: 35px;
  margin-bottom: 35px;
}

.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.my-45 {
  margin-top: 45px;
  margin-bottom: 45px;
}

.my-50 {
  margin-top: 50px;
  margin-bottom: 50px;
}

/*-- margin Top --*/
.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

/*-- margin Bottom --*/
.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mt-60 {
  margin-top: 60px;
}

/*-- margin Left --*/
.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-50 {
  margin-left: 50px;
}

/*-- margin Right --*/
.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-35 {
  margin-right: 35px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-45 {
  margin-right: 45px;
}

.mr-50 {
  margin-right: 50px;
}

.mt-n1 {
  margin-top: -0.25rem;
}

.mt-n2 {
  margin-top: -0.5rem;
}

.mt-n3 {
  margin-top: -1rem;
}

.mt-n4 {
  margin-top: -1.5rem;
}

.mt-n5 {
  margin-top: -3rem;
}

.mb-n1 {
  margin-bottom: -0.25rem;
}

.mb-n2 {
  margin-bottom: -0.5rem;
}

.mb-n3 {
  margin-bottom: -1rem;
}

.mb-n4 {
  margin-bottom: -1.5rem;
}

.mb-n5 {
  margin-bottom: -3rem;
}

/* Medium devices */
@media (max-width: 991px) {
  .ml-md-auto {
    margin-left: auto;
  }
}

.space,
.space-top {
  padding-top: var(--section-space);
}

.space,
.space-bottom {
  padding-bottom: var(--section-space);
}

.space-page,
.space-top-page {
  padding-top: var(--section-space);
}

.space-page,
.space-bottom-page {
  padding-bottom: calc(var(--section-space) + 40px);
}

.space-shape-plus,
.space-top-shape-plus {
  padding-top: calc(var(--section-space) + 20px);
}

.space-shape-plus,
.space-bottom-shape-plus {
  padding-bottom: calc(var(--section-space) + 20px);
}

.space-shape-min,
.space-top-shape-min {
  padding-top: calc(var(--section-space) - 50px);
}

.space-shape-min,
.space-bottom-shape-min {
  padding-bottom: calc(var(--section-space) - 50px);
}

.space-shape2-plus,
.space-top-shape2-plus {
  padding-top: calc(var(--section-space) + 60px);
}

.space-shape2-plus,
.space-bottom-shape2-plus {
  padding-bottom: calc(var(--section-space) + 60px);
}

.space-plus,
.space-top-plus {
  padding-top: calc(var(--section-space) + 30px);
}

.space-plus,
.space-bottom-plus {
  padding-bottom: calc(var(--section-space) + 30px);
}

.space-min,
.space-top-min {
  padding-top: calc(var(--section-space) - 30px);
}

.space-min,
.space-bottom-min {
  padding-bottom: calc(var(--section-space) - 30px);
}

.space-extra,
.space-extra-top {
  padding-top: calc(var(--section-space) - 30px);
}

.space-extra,
.space-extra-bottom {
  padding-bottom: calc(var(--section-space) - 30px);
}

.space-double,
.space-double-top {
  padding-top: 170px;
}

.space-double,
.space-double-bottom {
  padding-bottom: 170px;
}

/* Large devices */
@media (max-width: 1199px) {

  .space-double,
  .space-double-top {
    padding-top: 150px;
  }

  .space-double,
  .space-double-bottom {
    padding-bottom: 150px;
  }
}

/* Medium devices */
@media (max-width: 991px) {

  .space,
  .space-top {
    padding-top: var(--section-space-mobile);
  }

  .space,
  .space-bottom {
    padding-bottom: var(--section-space-mobile);
  }

  .space-page,
  .space-top-page {
    padding-top: var(--section-space-mobile);
  }

  .space-page,
  .space-bottom-page {
    padding-bottom: calc(var(--section-space-mobile) + 10px);
  }

  .space-extra,
  .space-extra-top {
    padding-top: calc(var(--section-space-mobile) - 30px);
  }

  .space-extra,
  .space-extra-bottom {
    padding-bottom: calc(var(--section-space-mobile) - 30px);
  }

  .space-top-md-none {
    padding-top: 0;
  }

  .space-double,
  .space-double-top {
    padding-top: 130px;
  }

  .space-double,
  .space-double-bottom {
    padding-bottom: 130px;
  }

  .space-shape-plus,
  .space-top-shape-plus {
    padding-top: calc(var(--section-space-mobile) + 18px);
  }

  .space-shape-plus,
  .space-bottom-shape-plus {
    padding-bottom: calc(var(--section-space-mobile) + 18px);
  }

  .space-shape-plus.blog-section,
  .space-bottom-shape-plus.blog-section {
    padding-bottom: calc(var(--section-space-mobile) - 7px);
  }

  .space-shape-min,
  .space-top-shape-min {
    padding-top: calc(var(--section-space-mobile) - 43px);
  }

  .space-shape-min,
  .space-bottom-shape-min {
    padding-bottom: calc(var(--section-space-mobile) - 43px);
  }

  .space-shape-min.blog-section,
  .space-bottom-shape-min.blog-section {
    padding-bottom: calc(var(--section-space-mobile) - 65px);
  }

  .space-shape2-plus,
  .space-top-shape2-plus {
    padding-top: calc(var(--section-space-mobile) + 20px);
  }

  .space-shape2-plus,
  .space-bottom-shape2-plus {
    padding-bottom: calc(var(--section-space-mobile) + 20px);
  }

  .space-plus,
  .space-top-plus {
    padding-top: calc(var(--section-space-mobile) + 20px);
  }

  .space-plus,
  .space-bottom-plus {
    padding-bottom: calc(var(--section-space-mobile) + 20px);
  }

  .space-min,
  .space-top-min {
    padding-top: calc(var(--section-space-mobile) - 20px);
  }

  .space-min,
  .space-bottom-min {
    padding-bottom: calc(var(--section-space-mobile) - 20px);
  }
}

/* Small devices */
@media (max-width: 767px) {

  .space-double,
  .space-double-top {
    padding-top: var(--section-space-mobile);
  }

  .space-double,
  .space-double-bottom {
    padding-bottom: var(--section-space-mobile);
  }

}


.hero-slider3 .hero-content3 {
  top: -50px;
}

.hero-slider3 .hero-content3 img {
  max-width: 500px;
}

.counter-number span {
  font-size: 35px;
}

.bg-t-color {
  background-color: var(--theme-color);
}

.hesap-color {
  background-color: var(--theme-color);
  color: white;
}

/* hasan mobil menu */

.background-image {
  background-position: left;
}

.header-menu-area.v4 .lower-header {
    background-color: rgb(41 37 31 / 39%);

}

.main-menu a {
    color: #ffffff;
}

.service-card-inner.program{
  background-color: #e3e8ed;
}
.service-card-inner.program .sr-title{
  color: #0ba6b8;
}

.service-card-inner.program:hover{
  background-color: #0ba6b8;
}

.service-card-inner.program:hover .sr-title{
  color: #ffffff;
}
#first-tab-group li a {
    padding: 10px;
  }

.kayan-yazi {
    width: 450px;
  }

.kayan-yazi marquee {
    width: 450px;
    font-weight: 600;
    font-size: 18px;
  }

.kayan-yazi-mobil {
  display: none;
}

@media (max-width: 991px) {
  .vs-hero-wrapper-four {
        padding: 130px 0 50px;
    }

  .vs-hero-wrapper-four {
    margin-top: -110px;
  }

  .header-top-area .header-social i {
    font-size: 28px;
  }

  .vs-header {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .header-top-area {
    padding-top: 22px;
  }

  .vs-menu-toggle {
    background-color: #fff;
    color: #136693;
  }

  .header-menu-area.v4 .lower-header {
    background-color: rgb(255 255 255 / 0%) !important;
  }

  .header-top-area.v4 .header-social ul li a {
    width: 42px;
    height: 42px;
  }

  .mobil-order-1 {
    order: 1!important;
  }

  .mobil-order-2 {
    order: 2!important;
  }

  .mobil-order-3 {
    order: 3!important;
  }

  .about-logo {
        text-align: center;
  }

  .kayan-yazi {
    display: none;
  }
  .kayan-yazi-mobil {
    display: block;
    background-color: #0a4c70;
  }
  .kayan-yazi-mobil marquee{
    color: #ffffff;
    font-weight: 600;
    font-size: 18px;
    padding-top: 10px;
  }


}

@media (max-width: 767px) {

  .vs-menu-toggle {
    background-color: #fff;
    color: #136693;
    padding: 8px;
  }
  #first-tab-group li a {
    padding: 4px;
    font-size: 14px;
  }

  .kayan-yazi {
    display: none;
  }
  .kayan-yazi-mobil {
    display: block;
    background-color: #0a4c70;
  }
  .kayan-yazi-mobil marquee{
    color: #ffffff;
    font-weight: 600;
    font-size: 18px;
    padding-top: 10px;
  }

}

.e-sertifika {
  background-color: #f8951e;
    border: 2px solid #fff;
    border-radius: 20px;
    padding: 6px 6px !important;
  min-height: 77px;
  display: flex!important;
  align-items: center;
}

.bildiri-kitabi {
  background: linear-gradient(135deg, #f8951e, #ffcc33, #f8951e);
  background-size: 200% 200%;
  border: 2px solid #fff;
  border-radius: 20px;
  padding: 3px 6px !important;
  min-height: 77px;
  color: #fff;
  text-align: center;
  font-weight: bold;
  display: flex!important;
  align-items: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  animation: glowAnimation 3s infinite ease-in-out;
}

/* Parlama animasyonu */
@keyframes glowAnimation {
  0% {
    background-position: -200% -200%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: -200% -200%;
  }
}


.vs-mobile-menu ul li a.bildiri-kitabi {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1;
  font-size: 16px;
  text-transform: capitalize;
  color: transparent; /* Metni şeffaf yapıyoruz */
  background: linear-gradient(to right, white, #f8951e);
  background-clip: text;
  -webkit-background-clip: text; /* Safari desteği */
  -webkit-text-fill-color: transparent; /* Safari'de metni şeffaf yapar */
  animation: textGradient 3s infinite ease-in-out;
}

/* Animasyon */
@keyframes textGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}


.vs-mobile-menu ul li a.bildiri-kitabi-mobil {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1;
  font-size: 16px;
  text-transform: capitalize;
  color: transparent; /* Metni şeffaf yapıyoruz */
  background: linear-gradient(to right, #f8951e, black, #f8951e);
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text; /* Safari desteği */
  -webkit-text-fill-color: transparent; /* Safari'de metni şeffaf yapar */
  animation: reflectionAnimation 3s infinite linear;
}

/* Yansıma animasyonu */
@keyframes reflectionAnimation {
  0% {
    background-position: 200% 0%;
  }
  100% {
    background-position: -200% 0%;
  }
}
