.tabgroup div {
  padding: 8px;
}
.tabgroup div .tabgroup div {
  padding: 0px;
}
.elementor-button {
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.cu_btn {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.cu_animated_button .cu_animated_dashes_border {
  stroke: #fff;
}
a.elementor-button,
.elementor-button {
  color: #fff;
  background-color: #bf1f2f;
  border-radius: 30px;
}
a.elementor-button,
.elementor-button:hover {
  color: #fff;
  background-color: #bf1f2f;
  border-radius: 30px;
}
.pc-button.elementor-button.size-lg {
  border-radius: 30px;
  padding: 19px 38.15px;
  font-size: 18px;
}
.pc-button.elementor-button {
  position: relative;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  line-height: 18px;
  min-height: 58px;
  min-width: 180px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #bf1f2f;
  border: 0 transparent;
}
.elementor-button {
  display: inline-block;
  line-height: 1;
  background-color: #818a91;
  color: #fff;
  fill: #fff;
  text-align: center;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  font-weight: 700;
}
.pc-button.elementor-button .button-content-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  float: none;
}
.elementor-button .elementor-button-text {
  display: inline-block;
}
.pc-button.elementor-button .inner-dashed-border {
  stroke: #fff;
}
.pc-button.elementor-button svg.inner-dashed-border {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  animation: dashed_border_running 20s linear infinite both;
  animation-play-state: paused;
  stroke-width: 2;
  stroke-dasharray: 9, 5;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  fill: none;
  transition: 0.4s;
}
.pc-button.elementor-button:hover .animated-dashes.inner-dashed-border {
  animation-play-state: running;
}
.cu_btn {
  display: inline-block;
  padding: 21px 40px;
  text-transform: capitalize;
  line-height: 16px;
  font-size: 17px;
  font-weight: 500;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-family: nunito, sans-serif;
}
@media (max-width: 767.98px) {
  .cu_btn {
    padding: 15px 25px;
    font-size: 15px;
  }
}
.cu_btn.btn_1 {
  background-color: transparent;
  border: 1px solid #bf1f2f;
  color: #bf1f2f;
}
.cu_btn.btn_1:hover {
  background-color: #bf1f2f;
  border: 1px solid #bf1f2f;
  color: #fff;
}
.cu_btn.btn_2 {
  background-color: #bf1f2f;
  border: 1px solid #bf1f2f;
  color: #fff;
  padding: 21px 55px;
}
.cu_btn.btn_2:hover {
  background-color: transparent;
  border: 1px solid #bf1f2f;
  color: #bf1f2f;
}
.cu_btn.white_btn {
  background-color: transparent;
  border: 1px solid #fff;
  color: #fff;
}
.cu_btn.white_btn:hover {
  background-color: transparent;
  border: 1px solid #bf1f2f;
  color: #fff;
  background-color: #bf1f2f;
}
.cu_btn.white_bg {
  background-color: #fff;
  border: 1px solid #fff;
  color: #bf1f2f;
}
.cu_btn.white_bg:hover {
  background-color: transparent;
  border: 1px solid #fff;
  color: #fff;
  background-color: #bf1f2f;
}
.cu_btn.btn_3 {
  background-color: #f9ae15;
  border: 1px solid #f9ae15;
  color: #fff;
}
.cu_btn.btn_3:hover {
  background-color: transparent;
  border: 1px solid #f9ae15;
  color: #f9ae15;
}
.cu_btn.tag_btn {
  background-color: #f0f2f9;
  border: 1px solid #f0f2f9;
  color: #5a5a77;
  border-radius: 5px;
  margin-right: 7px;
  margin-bottom: 5px;
  margin-top: 5px;
  font-weight: 500;
  font-family: quicksand, sans-serif;
  font-size: 14px;
  padding: 6px 12px;
}
.cu_btn.tag_btn:hover {
  background-color: transparent;
  border: 1px solid #bf1f2f;
  color: #fff;
  background-color: #bf1f2f;
}
.animated_border_effect {
  position: relative;
  background-color: #bf1f2f;
  border: 1px solid #bf1f2f;
  color: #fff;
  padding: 21px 55px;
}
.animated_border_effect #border_animation {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.animated_border_effect #chain_border_animation {
  -webkit-animation: dash 1s infinite linear;
  -moz-animation: dash 1s infinite linear;
  -o-animation: dash 1s infinite linear;
  animation: dash 1s infinite linear;
}
@-webkit-keyframes dash {
  to {
    stroke-dashoffset: 5;
  }
}
@-moz-keyframes dash {
  to {
    stroke-dashoffset: 5;
  }
}
@-o-keyframes dash {
  to {
    stroke-dashoffset: 5;
  }
}
@keyframes dash {
  to {
    stroke-dashoffset: 5;
  }
}

.event_part {
  position: relative;
  overflow: hidden;
  background: #fbfcfc;
}
.event_part .single_event_list {
  background-color: #bf1f2f;
  margin-bottom: 10px !important;
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px 10px;
  border-radius: 5px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.event_part .single_event_list:focus {
  box-shadow: 0 3px 10px rgb(74 74 74);
}
.event_part .single_event_list:hover {
  box-shadow: 0px 0px 20px 3px rgb(162 162 162);
}

@media (max-width: 767.98px) {
  .event_part .single_event_list {
    padding: 4px 10px;
    margin-bottom: 6px !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .single_event_list {
    padding: 4px 10px;
  }
}
.event_part .single_event_list:last-child {
  margin-bottom: 0;
}
.event_part .single_event_list .event_date {
  border: 1px solid #a81123;
  background-color: #a81123;
  color: #fff;
  width: 75px;
  height: 75px;
  text-align: center;
  border-radius: 50%;
  font-size: 30px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 767.98px) {
  .event_part .single_event_list .event_date {
    height: 65px;
    width: 65px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .single_event_list .event_date {
    height: 75px;
    width: 75px;
  }
}
.event_part .single_event_list .event_date h3 {
  color: #fff;
  font-family: nunito, sans-serif;
  margin-bottom: 0;
  line-height: 25px;
  font-weight: 700;
  font-size: 25px;
}

@media (max-width: 767.98px) {
  .event_part .single_event_list .event_date h3 {
    line-height: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .single_event_list .event_date h3 {
    line-height: 20px;
  }
}
.event_part .single_event_list .event_date span {
  font-size: 15px;
  display: block;
  font-weight: 400;
  line-height: 15px;
}
@media (max-width: 767.98px) {
  .event_part .single_event_list .event_date span {
    font-size: 14px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .single_event_list .event_date span {
    font-size: 14px;
  }
}
.event_part .single_event_list:nth-child(2) {
  background-color: #136693;
}
.event_part .single_event_list:nth-child(2):focus {
  box-shadow: 0px 0px 20px 3px rgb(162 162 162);
}
.event_part .single_event_list:nth-child(2) .event_date {
  border: 1px solid #105d8f;
  background-color: #105d8f;
}
.event_part .single_event_list:nth-child(3) {
  background-color: #bf1f2f;
}
.event_part .single_event_list:nth-child(3):focus {
  box-shadow: 0px 0px 20px 3px rgb(162 162 162);
}
.event_part .single_event_list:nth-child(3) .event_date {
  border: 1px solid #a81123;
  background-color: #a81123;
}
.event_part .single_event_list:nth-child(4) {
  background-color: #136693;
}
.event_part .single_event_list:nth-child(4):focus {
  box-shadow: 0px 0px 20px 3px rgb(162 162 162);
}
.event_part .single_event_list:nth-child(4) .event_date {
  border: 1px solid #105d8f;
  background-color: #105d8f;
}
.event_part .single_event_list .event_content {
  padding-left: 10px;
}
.event_part .single_event_list .event_content h4 {
  font-size: 18px;
  font-family: nunito, sans-serif;
  color: #fff;
  margin-bottom: 0px;
  font-weight: 700;
}
@media (max-width: 767.98px) {
  .event_part .single_event_list .event_content h4 {
    font-size: 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .single_event_list .event_content h4 {
    font-size: 18px;
  }
}
.event_part .single_event_list .event_content h4 a {
  color: #fff;
}
.event_part .single_event_list .event_content h4 a:hover {
  opacity: 0.9;
}
.event_part .single_event_list .event_content p {
  color: #fff;
}
.event_part .event_part_iner {
  position: relative;
}
.event_part .event_part_iner .event_img {
  position: absolute;
  left: -72px;
  height: 100%;
}
@media (max-width: 767.98px) {
  .event_part .event_part_iner .event_img {
    display: none;
  }
}
.event_part .event_part_iner .event_img img {
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
  background-color: #bfbfbf;
  box-shadow: 0 5px 30px 0 rgba(254, 75, 123, 0.1);
}
@media only screen and (min-width: 768px) and (max-width: 991.98px) {
  .event_part .event_part_iner .event_img img {
    max-width: 87%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199.98px) {
  .event_part .event_part_iner .event_img img {
    max-width: 87%;
  }
}
.event_part [class^="event_animation_"],
.event_part [class*=" event_animation_"] {
  position: absolute;
  z-index: -1;
}
.event_part .event_animation_1 {
  left: 5%;
  top: 10%;
}
@media (max-width: 767.98px) {
  .event_part .event_animation_1 {
    left: 5%;
    top: 0%;
  }
}
.event_part .event_animation_2 {
  left: 5%;
  top: 55%;
}
.event_part .event_animation_3 {
  right: 8%;
  top: 28%;
}
.event_part .event_animation_4 {
  right: 8%;
  top: 65%;
}



.gun_title h2 {
  font-size: 22px;
  font-family: nunito, sans-serif;
  color: #fff;
  font-weight: 700;
  margin-bottom: 0px;
}

.gun_title {
  border-radius: 5px;
  text-align: center;
}

#first-tab-group ul {
  border: 1px solid;
  margin-top: 10px;
  border-color: #fff;
  padding: 0;
}
#first-tab-group li {
  border: 1px solid;
  border-color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  background-color: #3489b7;
  border-radius: 5px;
}

#first-tab-group li:hover {
  box-shadow: 0 3px 10px rgb(0 0 0 / 50%);
}
#first-tab-group li:focus {
  box-shadow: 0 3px 10px rgb(0 0 0 / 50%);
}
#first-tab-group li:after {
  box-shadow: 0 3px 10px rgb(0 0 0 / 50%);
}

#first-tab-group li a {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  color: #fff;
  padding: 10px;
}

#first-tab-group li a.active {
  font-weight: bold;
  background-color: white;
  color: #3489b7;
  border: 2px solid #3489b7;
  border-radius: 5px;
}
.event_part p {
  font-size: 14px;
  margin: 0 0 0 0;
}
.tabs {
  display: flex;
  justify-content: center;
}
.wrapper .ilkgun {
  margin-right: 10px;
}
.wrapper .aragun {
  margin-right: 10px;
}

.wrapper ul {
  padding: 0px;
}
@media (max-width: 991px) {
  .tabs {
    display: block;
  }
  .mt-5,
  .my-5 {
    margin-top: 1rem !important;
  }
  .wrapper .ilkgun {
    margin-right: 0px !important;
  }
  .wrapper .aragun {
    margin-right: 0px !important;
  }
  .wrapper .songun {
    margin-right: 0px !important;
  }

  .tabgroup div {
    padding: 5px;
  }
}

@media (min-width: 991px) {
  #first-tab-group li a {
  }
}
@keyframes dashed_border_running {
  100% {
    stroke-dashoffset: -1000;
  }
}
table td.tg-0lax {
  color: #000000 !important;
}

.tg {
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0px auto;
}

.tg td {
  font-family: Arial, sans-serif;
  font-size: 14px;
  overflow: hidden;
  padding: 10px 5px;
  word-break: normal;
}

.tg th {
  font-family: Arial, sans-serif;
  font-size: 18px;
  font-weight: normal;
  overflow: hidden;
  padding: 10px 5px;
  word-break: normal;
}

.tg .tg-nan5 {
  background-color: #3389b7;
  color: #fff;
  font-weight: bold;
  text-align: center;
  vertical-align: top;
  border-radius: 0 10px 10px 0;
}

.tg .tg-deq2 {
  background-color: #3389b7;
  color: #fff;
  font-weight: bold;
  text-align: center;
  vertical-align: middle;
  border-radius: 10px 0 0 10px;
}
.tg .tg-deq3 {
  background-color: #3389b7;
  color: #fff;
  font-weight: bold;
  text-align: center;
  vertical-align: middle;
  border-radius: 0px 0 0 0px;
}

.tg .tg-3toq {
  background-color: #3389b7;
  color: #fff;
  font-weight: bold;
  text-align: center;
  vertical-align: top;
  border-radius: 0 10px 10px 0;
}

.tg .tg-0lax {
  background-color: #fde4ab;
  text-align: left;
  vertical-align: top;
  border-radius: 0 10px 10px 0;
}

.tg tr {
  border: 3px solid #fff;
}


@media screen and (max-width: 767px) {
  .tg {
    width: auto !important;
  }

  .tg col {
    width: auto !important;
  }

  .tg-wrap {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: auto 0px;
  }
}