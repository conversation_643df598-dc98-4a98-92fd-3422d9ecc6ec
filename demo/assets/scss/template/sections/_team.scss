.team-card {
    --gap: 40px;
    text-align: left;
    position: relative;
    margin-bottom: 30px;

    .team-img {
        position: relative;
        margin-left: auto;
        margin-right: auto;
        max-width: calc(100% - var(--gap)*2);
        mask-size: contain;
        mask-repeat: no-repeat;
        z-index: 2;

        &:before {
            content: "";
            @include equal-size(100%);
            position: absolute;
            background-color: $title-color;
            top: 0;
            left: 0;
            z-index: 0;
            visibility: hidden;
            opacity: 0;
            transition: .4s ease-in-out;
        }

        img {
            width: 100%;
        }
    }

    .team-content {
        @include equal-size(100%);
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        flex-direction: column;
        padding: 30px;
    }

    .team-text {
        visibility: hidden;
        opacity: 0;
        transform: translateY(-60px);
        transition: 0.4s ease-in-out;
        color: $white-color;
    }

    .team-info {
        border-radius: 30px;
        box-shadow: 0px 13px 16px 0px rgba(226, 222, 208, 0.07);
        background-color: #fff;
        text-align: center;
        padding: 180px 40px 35px 40px;
        margin-top: -155px;
    }

    .team-title {
        color: $title-color;
        margin-bottom: 0;

        a {
            color: inherit;

            &:hover {
                color: $theme-color;
            }
        }
    }

    .expand-btn {
        box-shadow: 0px 10px 15px 0px rgba(#9C29B2, 0.25);
        position: absolute;
        bottom: 120px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3;
    }

    .multi-social {
        --icon-size: 40px;

        a {
            i {
                margin-right: 4px;
                background-color: $white-color;
                color: $theme-color;
            }

            visibility: hidden;
            opacity: 0;
            transform: translateY(60px);
            transition: 0.4s ease-in-out;

            &:nth-child(1) {
                transition-delay: 0s;
            }

            &:nth-child(2) {
                transition-delay: 0.1s;
            }

            &:nth-child(3) {
                transition-delay: 0.2s;
            }

            &:nth-child(4) {
                transition-delay: 0.3s;
            }

            &:hover {
                i {
                    background-color: $theme-color;
                    color: $white-color;
                }
            }
        }
    }

    &:hover {
        .team-img {
            &:before {
                visibility: visible;
                opacity: 0.8;
            }
        }

        .team-text {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
        }

        .multi-social {
            a {
                visibility: visible;
                opacity: 1;
                transform: translateY(0);
            }
        }

        .expand-btn {
            .fa-share-alt {
                &:before {
                    content: "\f062";
                    font-weight: 300;
                }
            }
        }
    }
}

.team-box {
    position: relative;
    margin-bottom: 30px;

    .team-img {
        border-radius: 30px;
        overflow: hidden;

        img {
            transition: all ease 0.4s;
            transform: scale(1.002);
            border-radius: 30px;
        }
    }

    .multi-social {
        width: var(--icon-size);
        position: absolute;
        top: 40px;
        right: 40px;

        a {
            visibility: hidden;
            opacity: 0;
            transform: translateX(40px);
            transition: 0.4s ease-in-out;

            &:nth-child(1) {
                transition-delay: 0s;
            }

            &:nth-child(2) {
                transition-delay: 0.1s;
            }

            &:nth-child(3) {
                transition-delay: 0.2s;
            }

            &:nth-child(4) {
                transition-delay: 0.3s;
            }

            i {
                margin-bottom: 10px;
            }
        }
    }

    .team-info {
        max-width: 260px;
        border-radius: 30px;
        background-color: $white-color;
        box-shadow: 0px 15px 30px 0px rgba(37, 69, 116, 0.045);
        text-align: center;
        margin: 0 auto;
        padding: 25px 10px 20px 10px;
        margin-top: -46px;
        z-index: 2;
        position: relative;
    }

    .team-title {
        font-weight: 600;
        margin-bottom: 0;
        line-height: 1;

        a {
            color: inherit;

            &:hover {
                color: $theme-color;
            }
        }
    }

    &:hover {
        .team-img {
            img {
                transform: scale(1.07);
                @include safariNoScale();
            }
        }
        
        .multi-social {
            a {
                visibility: visible;
                opacity: 1;
                transform: translateX(0);
            }
        }
    }
}

/* ------- Team Details */
.team-details {
    position: relative;

    .team-content-card {
        position: absolute;
        bottom: 0;
    }

    .team-img {
        position: relative;
        z-index: 2;
        max-width: 490px;
    }
}

.team-content-card {
    --border-gap: 30px;
    padding: 75px 80px 75px 270px;
    border-radius: 100px;
    margin: var(--border-gap);
    position: relative;

    &:after {
        content: "";
        @include equal-size(calc(100% + var(--border-gap)*2));
        position: absolute;
        top: calc(0px - var(--border-gap));
        left: calc(0px - var(--border-gap));
        border: 2px dashed $theme-color;
        border-radius: inherit;
        z-index: -1;
    }

    .team-name {
        font-size: 40px;
        margin-bottom: 0;
    }

    .team-desig {
        display: block;
        margin-bottom: 17px;
        color: $theme-color;
        font-size: 18px;
    }

    .info-list {
        ul {
            padding-left: 0;
            list-style: none;
            margin-bottom: 0;
        }

        li {
            color: $body-color;
            font-family: $body-font;
            font-size: 20px;
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }

            a {
                color: inherit;

                &:hover {
                    color: $theme-color;
                }
            }
        }

        strong {
            color: $title-color;
            font-weight: 600;
            margin-right: 10px;
        }
    }
}

// .team-section-two
.team-section-two{
    position: relative;
    .slick-arrow.slick-next{
        margin-left: 0;
        margin-right: 64px;
    }
    .slick-arrow.slick-prev{
        margin-right: 0;
        margin-left: 64px;
    }
    .scale{
        position: absolute;
        top: 212px;
        left: 140px;
        @include xxl{
            display: none !important;
        }
    }
    .kids{
        position: absolute;
        bottom: 30px;
        right: 100px;
        @include xxl{
            display: none !important;
        }
    }
}

// team-card-two
.team-card-two{
    position: relative;
    margin-bottom: 40px;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.07);
    &:hover {
        .image{
            img{
                transform: scale(1.1);
            }
        }
        .social-links {
            transform: scaleY(1);
            opacity: 1;
            visibility: visible;
        }
    }

    .team-img {
        overflow: hidden;
        position: relative;
        margin-bottom: 0;
        border-radius: 20px 20px 0 0;
        z-index: 1;
        .image {
            img {
                width: 100%;
                transition: all 400ms ease;
            }
        }
    }

    .team-info {
        position: relative;
        padding: 50px 30px 31px;
        text-align: center;
        z-index: 2;
        .name {
            margin-bottom: 6px;
            color: var(--title-color);
            font-weight: 700;
            a{
                color: inherit;
            }
            &:hover{
                color: var(--theme-color);
            }
        }
        .designation {
            position: relative;
            display: block;
            transition: all 400ms ease;
        }
    }

    .share-icon {
        position: absolute;
        top: -25px;
        right: 30px;
        height: 50px;
        width: 50px;
        line-height: 50px;
        text-align: center;
        font-size: 16px;
        color: #ffffff;
        background-color: var(--theme-color);
        transition: all 300ms ease;
        border-radius: 50%;
        z-index: 3;
    }

    .social-links {
        position: absolute;
        right: 30px;
        bottom: 123%;
        display: flex;
        align-items: center;
        flex-direction: column;
        transform: scaleY(0);
        transform-origin: bottom;
        z-index: 3;
        opacity: 0;
        visibility: hidden;
        transition: all 400ms ease;
        background-color: var(--theme-color);
        border-radius: 50px;
        padding: 26px 0 23px;
        a {
            position: relative;
            width: 50px;
            text-align: center;
            margin-top: 9px;
            font-size: 16px;
            color: #ffffff;
            background-color: var(--bg-theme-color1);
            border-radius: 10px;
            transition: all 300ms ease;
            display: inline-block;
            &:first-child{
                margin-top: 0;
            }
            &:hover {
                color: var(--secondary-color);
            }
        }
    }
}

@include lg {
    .team-card {
        --gap: 25px;
    }

    .team-details .team-img {
        max-width: 450px;
    }

    .team-content-card {
        --border-gap: 20px;
        padding: 30px 30px 30px 270px;
        border-radius: 50px;

        .team-name {
            font-size: 30px;
        }
    }
}

@include md {
    .team-details {
        .team-content-card {
            position: relative;
            bottom: unset;
            z-index: 3;
        }
    }

    .team-content-card {
        padding: 30px 40px;

        .team-name {
            font-size: 30px;
        }
    }
}

@include sm {
    .team-card {
        --gap: 40px;
    }

    .team-content-card {
        --border-gap: 15px;
        padding: 20px 25px;
        border-radius: 30px;

        .info-list li {
            font-size: 18px;
        }
    }
}

@include xs {
    .team-content-card {
        .team-name {
            font-size: 26px;
        }

        .info-list li {
            font-size: 16px;
        }
    }
}

@include vxs {
    .team-card {
        --gap: 25px;

        .team-info {
            padding: 165px 40px 20px 40px;
        }

        .expand-btn {
            bottom: 90px;
        }
    }

    .team-box {
        .multi-social {
            top: 25px;
            right: 25px;
        }
    }
}

@media (max-width: 350px) {
    .team-card {
        .team-content {
            padding: 10px;
        }

        .team-text {
            font-size: 14px;
            line-height: 1.4;
        }
    }
}