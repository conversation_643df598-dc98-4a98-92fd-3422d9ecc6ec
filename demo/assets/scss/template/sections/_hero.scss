.vs-hero-wrapper,
.breadcrumb-section {
  .section-before {
    top: -5px;
  }
}

.hero-slider1 {
  position: relative;

  .vs-hero-inner {
    padding: 100px 0 140px 0;
  }
}

.vs-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    opacity: 0.55;
    @include equal-size(100%);
    object-fit: cover;
    object-position: center center;
  }
}

.hero-content {
  max-width: 660px;
  padding: 150px;
  right: 180px;
  margin-left: auto;
  text-align: center;
  position: relative;
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;

  .tr-element {
    background-color: $white-color;
    opacity: 0.9;
  }

  img.car {
    position: absolute;
    top: 17px;
    left: 63px;
  }
}

.con-before {
  position: absolute;
  left: -26px;
  top: 19%;
  transform: rotate(3deg);
}

.con-after {
  position: absolute;
  right: -26px;
  top: 20%;
  transform: rotate(3deg);
}

.hero-title {
  .focus {
    font-size: 90px;
    margin-bottom: -4px;
    line-height: 1.1;
    display: block;
  }

  &.style-2 {
    margin-bottom: 35px;
    font-size: 72px;
    color: $title-color;
  }
}

.hero-subtitle {
  &.style-2 {
    font-weight: 500;
    display: block;
    margin-bottom: 35px;
    color: $title-color;
  }
}

.hero-text {
  margin-bottom: 40px;
  color: $blue-color;

  span {
    font-weight: 600;
    font-size: 24px;
    position: relative;
  }

  i {
    font-size: 8px;
    margin: 0 20px;
    position: relative;
    top: -4px;
  }
}

// Hero 2
.hero-2 {
  position: relative;
  overflow: hidden;

  .section-after {
    margin-bottom: -35px;
  }

  .shape-mockup {
    position: absolute !important;
  }

  .vs-hero-inner {
    text-align: center;
    position: relative;
    z-index: 2;
    height: 840px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.hero-img-1 {
  position: absolute !important;
  top: 50px;
  left: 45px;
  z-index: 1;
}

.hero-img-2 {
  position: absolute !important;
  bottom: 80px;
  right: 60px;
  z-index: 1;
}

// Hero 3
.hero-slider3 {
  .slick-arrow {
    background-color: rgba(255, 255, 255, 0.1);
    color: $white-color;
    border: none;
    top: 50%;
    left: 80px;
    transform: translateY(-50%);
    width: var(--icon-size, 60px);
    height: var(--icon-size, 60px);
    z-index: 2;

    &.slick-next {
      right: 80px;
      left: unset;
    }

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }
}

.hero-3 {
  .vs-hero-inner {
    padding: 100px 0;
    height: 815px;
    display: flex;
    align-items: center;
  }
}

.hero-content3 {
  max-width: 760px;
  text-align: center;
  position: relative;
  z-index: 3;
  margin: 0 auto;

  .hero-title {
    color: $white-color;
  }
}

.hero-subtitle.style-3 {
  font-size: 20px;
  font-weight: 500;
  display: block;
  margin-bottom: 15px;
  color: $white-color
}

@include hd {
  div#cloud {
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

@include xxl {
  .hero-img-2 {
    bottom: 10px;
    right: 15px;
  }
}

@include ml {

  .vs-hero-wrapper,
  .breadcrumb-section {
    .section-before {
      top: -8px;
    }
  }

  .hero-content {
    right: 50px;
    margin-left: auto;
    text-align: center;
    max-width: 580px;
    padding: 130px 100px;
  }

  .hero-title .focus {
    font-size: 70px;
  }

  .hero-title.style-2 {
    font-size: 60px;
  }

  .hero-img-1 {
    left: 10px;
  }

  .hero-img-2 {
    max-width: 400px !important;
  }
}

@include lg {
  .vs-hero-wrapper {

    .shape-before,
    .section-before {
      img {
        height: 20px;
      }
    }
  }

  .hero-2 {

    .shape-after,
    .section-after {
      img {
        height: 80px;
      }
    }
  }

  .hero-3 {

    .shape-after,
    .section-after {
      img {
        height: 45px;
      }
    }
  }

  .hero-slider1 {
    .vs-hero-inner {
      padding: 100px 0 100px 0;
    }
  }

  .hero-content {
    right: unset;
    margin-right: auto;
    max-width: 560px;
  }

  .hero-2 {
    .vs-hero-inner {
      height: 520px;
    }

    #fire {
      max-width: 140px;
    }

    #rocket {
      bottom: 6% !important;
      left: 27% !important;
    }

    .section-after {
      margin-bottom: -20px;
    }
  }

  .hero-title.style-2 {
    font-size: 48px;
  }

  .hero-img-2 {
    bottom: 0;
  }

  .hero-img-1 {
    max-width: 300px !important;
  }

  .hero-img-2 {
    max-width: 320px !important;
  }

  .hero-3 .vs-hero-inner {
    height: 600px;
  }
}

@include md {
  .vs-hero-wrapper {

    .shape-before,
    .section-before {
      img {
        height: 15px;
      }
    }
  }

  .hero-title .focus {
    font-size: 50px;
  }

  .hero-subtitle.style-2.mb-35 {
    margin-bottom: 10px !important;
  }

  .hero-img-1 {
    display: none !important;
  }

  .hero-img-2 {
    display: none !important;
  }

  .hero-2 {
    #rocket {
      bottom: 5% !important;
      left: 10% !important;
    }

    .vs-hero-inner {
      padding: 120px 0 120px 0;
      height: auto;
    }
  }

  .hero-3 {
    .vs-hero-inner {
      height: auto;
    }
  }
}

@include sm {
  .hero-slider1 {
    .vs-hero-inner {
      padding: 80px 50px 60px 50px;
    }
  }

  .hero-content {
    background-size: contain;

    img.car {
      top: -25px;
    }
  }

  .con-before {
    display: none !important;
  }

  .con-after {
    display: none !important;
  }

  .hero-content {
    padding: 100px 20px 60px;
  }

  .hero-text {
    margin-bottom: 20px;
  }

  .hero-title {
    font-size: 32px;
    line-height: 1.6;

    .focus {
      font-size: 44px;
    }

    &.style-2 {
      font-size: 36px;
      line-height: 1.2;
      margin-bottom: 20px;
    }
  }

  .hero-subtitle.style-2 {
    margin-bottom: 20px;
  }

  .hero-2 {
    #rocket {
      max-width: 55px;
      bottom: 5% !important;
      left: 10% !important;
    }

    #fire {
      max-width: 100px;
      left: -10% !important;
    }

    #star {
      max-width: 40px;
    }
  }
}

@include xs {
  .hero-img-2 {
    display: none !important;
  }

  .hero-slider1 .vs-hero-inner {
    padding: 80px 25px 60px 25px;
  }

  .hero-content {
    padding: 60px 20px 40px;
  }

  .hero-content img.car {
    top: 68px;
    max-width: 90px;
    left: -10px;
  }

  .hero-text {
    margin-bottom: 8px;
  }

  .hero-title.mb-30 {
    margin-bottom: 15px;
  }
}

@media (max-width: 350px) {
  .hero-text {
    margin-bottom: 0;

    span {
      font-size: 20px;
    }
  }

  .hero-title {
    font-size: 26px;
    line-height: 1.4;
  }

  .hero-title .focus {
    font-size: 36px;
    line-height: 1;
  }

  .hero-content img.car {
    max-width: 60px;
  }
}

// hero 4
.vs-hero-wrapper-four {
  padding: 325px 0;
  margin-top: -50px;
  z-index: 3;

  @include lg {
    padding: 250px 0;
  }

  @include sm {
    padding: 200px 0;
    margin-top: 0;
  }

  @include vxs {
    padding: 150px 0;
  }

  .slideinup {
    opacity: 1;
  }

  .hero-content4 {
    position: relative;
    z-index: 3;
    max-width: 699px;

    .hero-title {
      color: var(--title-color);
      font-size: 72px;
      font-weight: 700;
      line-height: 82px;
      margin-bottom: 39px;

      @include sm {
        font-size: 65px;
        line-height: 68px;
      }

      @include sm {
        font-size: 56px;
        line-height: 59px;
      }

      @include vxs {
        font-size: 38px;
        line-height: 45px;
        margin-bottom: 25px;
      }
    }

    .hero-subtitle {
      font-size: 24px;
      font-weight: 500;
      display: block;
      color: var(--title-color);
      margin-bottom: 32px;

      @include vxs {
        font-size: 22px;
        margin-bottom: 15px;
      }
    }
  }
}