// Primary Button
.vs-btn {
  background-color: $theme-color;
  color: $white-color;
  font-size: 18px;
  font-weight: 500;
  padding: 11px 33px;
  line-height: 1.6;
  text-transform: capitalize;
  min-width: 150px;
  text-align: center;
  border-radius: 9999px;
  border: none;
  display: inline-block;
  overflow: hidden;
  position: relative;
  z-index: 2;

  i {
    font-size: 16px;
    margin-right: 5px;
  }

  &:hover {
    color: $white-color;
    background-color: $secondary-color;
  }

  &.btn-white {
    background-color: $white-color;
    color: $theme-color;

    &:hover {
      color: $white-color;
    }
  }

  &.style-1 {
    background-color: transparent;
    border: 1px solid $theme-color;
    color: $theme-color;
    padding: 10px 33px;

    &:hover {
      border-color: $secondary-color;
      color: $white-color;
    }
  }

  &.style-2 {
    width: 160px;
    background-color: rgba(255, 255, 255, 0.145);

    &:hover {
      background-color: $title-color;
      color: $white-color;
    }
  }

  &.btn-sm {
    padding: 9px 20px;
    min-width: 120px;
    height: 40px;
    font-size: 14px;
  }
}

/* Button Hover */
.wave-btn {
  transition: 0.21s;
}

.btn-hover {
  z-index: -1;
  position: absolute;
  left: -1px;
  top: -1px;
  width: 110%;
  height: 110%;
  border-radius: 50px;

  .btn-hover-inner {
    position: relative;
    display: block;
    height: 100%;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .part {
    position: absolute;
    top: -5px;
    width: 25%;
    height: 100%;
    border-radius: 100%;
    transform: translate3d(0, 150%, 0) scale(1.7);
    transition: transform 0.21s !important;
    background: $secondary-color;
    border-color: $secondary-color;

    &:nth-child(1) {
      left: 0;
      transition-delay: 0s !important;
    }

    &:nth-child(2) {
      left: 30%;
      transition-delay: 0.07s !important;
    }

    &:nth-child(3) {
      left: 60%;
      transition-delay: 0.14s !important;
    }

    &:nth-child(4) {
      left: 90%;
      transition-delay: 0.21s !important;
    }
  }
}

.wave-btn {

  &:hover,
  &:active,
  &:focus {
    transition-delay: 0.21s;

    .part {
      transform: translateZ(0) scale(1.7) !important;
    }
  }
}

@include vxs {
  .vs-btn {
    font-size: 14px;
    padding: 8px 25px;
    min-width: 140px;

    i {
      font-size: 14px;
    }

    &.btn-60 {
      height: 60px;
    }

    &.style-1 {
      padding: 7px 25px;
    }

    &.style-2 {
      width: 150px;
    }
  }
}

// Icon Btn
.icon-btn {
  display: inline-block;
  width: var(--btn-size, 50px);
  height: var(--btn-size, 50px);
  line-height: var(--btn-size, 52px);
  background-color: $theme-color;
  color: $white-color;
  text-align: center;
  border-radius: 50%;
  border: none;

  &:hover,
  &.active {
    background-color: $secondary-color;
    color: $white-color;
  }

  &.style-2 {
    background-color: transparent;
    border: 1px solid $theme-color;
    color: $theme-color;
    line-height: 1;

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }

  &.style-3 {
    background-color: $smoke-color;
    color: $theme-color;
    line-height: 1;

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }

  &.style-4 {
    background-color: $white-color;
    color: $theme-color;
    line-height: 1;

    &:hover {
      background-color: $theme-color;
      color: $white-color;
    }
  }
}

// Play Button
.play-btn {
  position: relative;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  color: $white-color;
  border: 2px solid;
  @include equal-size(100px);

  >i {
    color: $white-color;
    position: relative;
    z-index: 2;
    line-height: 97px;
    text-align: center;
    font-size: 32px;
    border-radius: inherit;
    transition: all ease 0.4s;
  }

  &.style-1 {
    background-color: $theme-color;
    border-color: $theme-color;

    &::before {
      content: "";
      position: absolute;
      border: 2px solid $theme-color;
      @include equal-size(150px);
      top: -25px;
      left: -25px;
      z-index: 1;
      @extend .ripple-animation;
      border-radius: 50%;

      @include xs {
        @include equal-size(70px);
        left: -12px;
        top: -12px;
      }
    }

    &::after {
      content: "";
      position: absolute;
      border: 2px solid $theme-color;
      @include equal-size(195px);
      top: -46px;
      left: -46px;
      z-index: 1;
      border-radius: 50%;
      @extend .ripple-animation;

      @include xs {
        @include equal-size(90px);
        left: -22px;
        top: -22px;
      }
    }

    &:hover {
      color: $theme-color;
      background-color: $white-color;
      border-color: $white-color;

      i {
        color: inherit;
      }
    }

    @include xs {
      @include equal-size(50px);

      i {
        font-size: 14px;
        line-height: 47px;
      }
    }
  }
}

// Link Button
.link-btn {
  font-size: 18px;
  font-weight: 500;
  position: relative;

  i {
    margin-left: 2px;
    transition: 0.3s ease-in-out;
    line-height: 1;
    position: relative;
    top: 2px;
  }

  &:before {
    content: "";
    height: 1px;
    width: 0;
    background-color: $theme-color;
    position: absolute;
    bottom: 3px;
    left: 0;
    transition: 0.3s ease-in-out;
  }

  &:hover {

    &,
    i {
      color: $secondary-color;
    }

    &:before {
      width: 100%;
    }
  }
}

//plain btn
.simple-icon {
  border: none;
  background-color: transparent;
  font-size: 20px;
  color: $title-color;
  margin: 0;
  padding: 0;

  &:hover,
  &.ative {
    color: $theme-color;
  }
}

// Scroll To Top
.scroll-btn {
  position: fixed;
  bottom: 300px;
  right: 30px;
  z-index: 94;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  border-radius: 50%;

  i {
    display: inline-block;
    background-color: $theme-color;
    color: $white-color;
    text-align: center;
    font-size: 16px;
    width: var(--btn-size, 50px);
    height: var(--btn-size, 50px);
    line-height: var(--btn-size, 50px);
    z-index: 2;
    border-radius: inherit;
    position: relative;
    transition: all ease 0.8s;
  }

  &:focus,
  &:hover {
    i {
      background-color: $secondary-color;
      color: $white-color;
    }
  }

  &.show {
    bottom: 120px;
    opacity: 1;
    visibility: visible;
  }

  @include sm {
    --btn-size: 40px;
    --extra-shape: -4px;
    right: 15px;
    bottom: 50px;

    &.show {
      bottom: 15px;
    }
  }
}

.scrollToTop {
  position: fixed;
  right: 30px;
  bottom: 500px;
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.4s;
  z-index: 96;

  &.show {
    bottom: 60px;
    opacity: 1;
    visibility: visible;
  }
}

@include sm {
  .play-btn {
    --icon-size: 60px;
  }

  .scrollToTop {
    right: 20px;

    &.show {
      bottom: 20px;
    }
  }
}