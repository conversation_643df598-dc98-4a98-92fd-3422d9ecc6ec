.sec-title {
  font-size: 48px;
  margin-top: -0.8rem;
  line-height: 1.208;

  &.big-title {
    font-size: 60px;
    margin-top: -1.1rem;
  }
}

.sub-title {
  font-size: 24px;
  font-weight: 500;
  color: $theme-color;
  display: block;
  margin-bottom: 29px;
  margin-top: -4px;
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 15px);
}

@include lg {
  .sec-title {
    font-size: 42px;

    &.big-title {
      font-size: 48px;
      margin-top: -0.8rem;
    }
  }
}

@include md {
  .title-area {
    margin-bottom: calc(var(--section-title-space) - 25px);
  }

  .sub-title {
    margin-bottom: 20px;
  }

  .sec-title {
    font-size: 36px;

    &.big-title {
      font-size: 40px;
    }
  }
}

@include sm {
  .sec-title {
    font-size: 30px;

    &.big-title {
      font-size: 36px;
    }
  }
}

@include xs {
  .sub-title {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .sec-title {
    font-size: 24px;

    &.big-title {
      font-size: 30px;
    }
  }
}