<!doctype html>
<html class="no-js" lang="TR">

<head>
    <?php include('head.php') ?>
    <link rel="canonical" href="https://gnctpk.org/" />
    <title>10. Genç Pediatristler Kongresi | Anasayfa</title>
</head>

<body>

    <?php include('header.php') ?>

    <!--======== Hero Section ========-->
    <section class="vs-hero-wrapper-four position-relative" data-bg-src="/demo/doc/banner-masaustu.jpg">
        <div class="container">
            <div class="hero-content3 d-flex align-items-center justify-content-center" style="flex-wrap: wrap;">
                <img src="/demo/doc/banner-masaustu-metin.png" class="d-none d-md-block">
                <img src="/demo/doc/banner-mobil-metin-v2.png" class="d-block d-md-none">
                <h4 class="metin-none" style="color: #fff; margin-top: 15px;">30 Ekim - 1 Kasım 2025<br>Wyndham Grand İstanbul Levent</h4>
            </div>
        </div>
        <!--
        <div class="shape-mockup movingTopLeft d-none d-md-block" data-bottom="43%" data-right="10%"><img src="/doc/kus.png" alt="shapes"></div>
    -->
    </section>
    <!--======== Hero Section ========-->

    <section class="counter-section" data-overlay="title" data-opacity="9">
        <div class="container">
            <div class="row wow fadeInUp" style="visibility: visible; animation-delay: 0.1s; animation-name: fadeInUp;">
                <div class="col-xl-6 col-lg-7 col-md-8 col-sm-9">
                    <div class="title-area">
                        <h2 class="sec-title" style="color: #fff">Kongreye Kalan Süre</h2>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 col-9">
                    <div class="row gy-30">
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="days" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="hours" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="minutes" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="seconds" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </section>
    <!--======== Service Section ========-->
    <section class="service-section space">
        <div class="container">
            <div class="row gx-lg-15 justify-content-center">
                <div class="col-xl-12 col-md-10 wow fadeInLeft" data-wow-delay="0.1s">
                    <div class="flex-wrap d-flex w-100 mb-4" style="justify-content: space-evenly;">
                        <button class="sr-icon-btn" type="button" href="/demo/kurullar" onclick="window.location.href='/demo/kurullar'">Kurullar</button>
                        <button class="sr-icon-btn" type="button" href="/demo/kurullar" onclick="window.location.href='/demo/bilimsel-program'">Bilimsel Program</button>
                        <button class="sr-icon-btn" type="button" href="/demo/kurullar" onclick="window.location.href='/demo/kayit-konaklama'">Kayıt & Konaklama</button>
                        <button class="sr-icon-btn" type="button" href="/demo/kurullar" onclick="window.location.href='/demo/bildiri-gonderimi'"><span class="text">Bildiri Gönderimi</span></button>
                    </div>
                </div>

            </div>
        </div>
    </section>
    <!--======== / Service Section ========-->




    <!--======== / Counter Section ========-->

    <!--======== About Section ========-->
    <section class="about-section pt-40 pb-30 mb-80" style="display: none;">
        <div class="container">
            <div class="row gy-30 align-items-center justify-content-center">
                <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.1s">
                    <span class="sub-title">10. Genç Pediatristler Kongresi</span>
                    <h2 class="sec-title big-title">Davet</h2>
                    <?php include('davet-metni.php') ?>
                </div>
            </div>

        </div>
    </section>
    <!--======== / About Section ========-->

    <?php include('footer.php') ?>
    <!-- <?php //include('popup.php') ?> -->
    <script>
        // Set the date we're counting down to
        var countDownDate = new Date("October 30, 2025 10:00:00").getTime();

        // Update the count down every 1 second
        var x = setInterval(function() {

            // Get today's date and time
            var now = new Date().getTime();

            // Find the distance between now and the count down date
            var distance = countDownDate - now;

            // Time calculations for days, hours, minutes and seconds
            var days = Math.floor(distance / (1000 * 60 * 60 * 24));
            var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Display the result in the element with id="demo"
            $("#days").html(days + "<br><span>Gün</span>");
            $("#hours").html(hours + "<br><span>Saat</span>");
            $("#minutes").html(minutes + "<br><span>Dakika</span>");
            $("#seconds").html(seconds + "<br><span>Saniye</span>");

            // If the count down is finished, write some text
            if (distance < 0) {
                clearInterval(x);
                document.getElementById("demo").innerHTML = "EXPIRED";
            }
        }, 1000);
    </script>

<?php include('script.php') ?>

</body>

</html>