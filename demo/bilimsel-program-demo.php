<!doctype html>
<html class="no-js" lang="TR">

<head>
    <?php include('head.php') ?>
    <link rel="canonical" href="https://gnctpk.org/bilimsel-program"/>
    <title>10. Genç Pediatristler Kongresi | Bilimsel Program</title>
</head>

<body>
<?php include('header.php') ?>
<?php include('banner-area.php') ?>
<section class="why-section space-shape-plus pt-0 mt-40">
    <div class="container">
        <div class="col-xl-12">
            <div class="row gx-70 gy-30 align-items-center justify-content-center">
                <div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.1s">
                    <div class="title-area">
                        <h2 class="sec-title">Bilimsel Program</h2>
                    </div>

                </div>
            </div>
            <div class="gx-70 gy-30 align-items-center justify-content-center">
                <div class="row">
                    <div class="col-lg-6 col-md-12 col-12 service-card wow fadeInUp" data-wow-delay="0.1s"
                         style="cursor: pointer; margin-bottom: 40px;">
                        <a href="/doc/9gnctpkprogram.pdf?v=12345" target="_blank">
                            <div class="service-card-inner program"
                                 style="padding: 20px 40px 5px 40px;">

                                <h2 class="sr-title h4">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                         version="1.1" width="60" height="60" viewBox="0 0 256 256"
                                         xml:space="preserve">
                                        <g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;"
                                           transform="translate(1.**************** 1.****************) scale(2.81 2.81)">
                                            <path d="M 78.806 62.716 V 20.496 c 0 -1.214 -0.473 -2.356 -1.332 -3.216 L 61.526 1.332 C 60.667 0.473 59.525 0 58.31 0 H 15.742 c -2.508 0 -4.548 2.04 -4.548 4.548 V 43.16 v 19.556 C 34.114 65.376 56.665 65.47 78.806 62.716 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(220,223,225); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 11.194 62.716 v 11.23 v 11.506 c 0 2.508 2.04 4.548 4.548 4.548 h 58.517 c 2.508 0 4.548 -2.04 4.548 -4.548 V 62.716 H 11.194 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(234,84,64); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <polygon points="60.27,18.41 78.81,36.88 78.73,19.73 "
                                                     style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(196,203,210); fill-rule: nonzero; opacity: 1;"
                                                     transform="  matrix(1 0 0 1 0 0) "/>
                                            <path d="M 77.474 17.28 L 61.526 1.332 c -0.675 -0.676 -1.529 -1.102 -2.453 -1.258 v 15.382 c 0 2.358 1.919 4.277 4.277 4.277 h 15.382 C 78.576 18.81 78.15 17.956 77.474 17.28 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(171,178,184); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 33.092 68.321 h -4.374 c -0.69 0 -1.25 0.56 -1.25 1.25 v 8.091 v 5.541 c 0 0.69 0.56 1.25 1.25 1.25 s 1.25 -0.56 1.25 -1.25 v -4.291 h 3.124 c 2.254 0 4.088 -1.834 4.088 -4.088 v -2.415 C 37.18 70.155 35.346 68.321 33.092 68.321 z M 34.68 74.824 c 0 0.876 -0.712 1.588 -1.588 1.588 h -3.124 v -5.591 h 3.124 c 0.876 0 1.588 0.712 1.588 1.588 V 74.824 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 45.351 84.453 H 41.27 c -0.69 0 -1.25 -0.56 -1.25 -1.25 V 69.571 c 0 -0.69 0.56 -1.25 1.25 -1.25 h 4.082 c 2.416 0 4.38 1.965 4.38 4.38 v 7.371 C 49.731 82.488 47.767 84.453 45.351 84.453 z M 42.52 81.953 h 2.832 c 1.037 0 1.88 -0.844 1.88 -1.881 v -7.371 c 0 -1.036 -0.844 -1.88 -1.88 -1.88 H 42.52 V 81.953 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 61.282 68.321 H 54.07 c -0.69 0 -1.25 0.56 -1.25 1.25 v 13.632 c 0 0.69 0.56 1.25 1.25 1.25 s 1.25 -0.56 1.25 -1.25 v -5.566 h 3.473 c 0.69 0 1.25 -0.56 1.25 -1.25 s -0.56 -1.25 -1.25 -1.25 H 55.32 v -4.315 h 5.962 c 0.69 0 1.25 -0.56 1.25 -1.25 S 61.973 68.321 61.282 68.321 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 60.137 40.012 c -0.154 -0.374 -0.52 -0.617 -0.924 -0.617 h -4.805 V 27.616 c 0 -0.552 -0.447 -1 -1 -1 H 40.592 c -0.552 0 -1 0.448 -1 1 v 11.778 h -4.805 c -0.404 0 -0.769 0.244 -0.924 0.617 c -0.155 0.374 -0.069 0.804 0.217 1.09 l 12.213 12.213 c 0.195 0.195 0.451 0.293 0.707 0.293 s 0.512 -0.098 0.707 -0.293 L 59.92 41.102 C 60.206 40.815 60.292 40.386 60.137 40.012 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(196,203,210); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                            <path d="M 58.137 38.012 c -0.154 -0.374 -0.52 -0.617 -0.924 -0.617 h -4.805 V 25.616 c 0 -0.552 -0.447 -1 -1 -1 H 38.592 c -0.552 0 -1 0.448 -1 1 v 11.778 h -4.805 c -0.404 0 -0.769 0.244 -0.924 0.617 c -0.155 0.374 -0.069 0.804 0.217 1.09 l 12.213 12.213 c 0.195 0.195 0.451 0.293 0.707 0.293 s 0.512 -0.098 0.707 -0.293 L 57.92 39.102 C 58.206 38.815 58.292 38.386 58.137 38.012 z"
                                                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(234,84,64); fill-rule: nonzero; opacity: 1;"
                                                  transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
                                        </g>
                                    </svg>
                                    Programı indirmek için tıklayınız
                                </h2>
                            </div>
                        </a>
                        
                    </div>
                    <div class="col-lg-10 col-md-12 col-12 service-card wow fadeInUp" data-wow-delay="0.1s"
                         style="cursor: pointer;">
                         <img src="/doc/bilimsel/9gnctpkprogram-0001.jpg">
                        <img src="/doc/bilimsel/9gnctpkprogram-0002.jpg">
                        <img src="/doc/bilimsel/9gnctpkprogram-0003.jpg">
                     </div>
                </div>
            </div>
        </div>
        <!-- <div class="shape-mockup movingX d-none d-xl-block" data-top="12%" data-left="4%"><img src="assets/img/icon/animal-1.png" alt="shapes"></div>
        <div class="shape-mockup moving d-none d-xl-block" data-bottom="10%" data-left="4%"><img src="assets/img/icon/halicopter.png" alt="shapes"></div>
        <div class="shape-mockup rotate d-none d-lg-block" data-top="13%" data-right="8%"><img src="assets/img/icon/sun-5.png" alt="shapes"></div>
        <div class="shape-mockup shapePulse d-none d-xl-block" data-bottom="18%" data-right="7%"><img src="assets/img/icon/dog-2.png" alt="shapes"></div> -->
</section>


<?php include('footer.php') ?>

<?php include('script.php') ?>

<script src="/js/script.js"></script>

</body>

</html>