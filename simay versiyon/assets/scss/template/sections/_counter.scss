.connter_icon {
    font-size: 40px;
    color: $theme-color;
    margin: 0 0 17px 0;
    transition: all ease 0.4s;

    i {
        line-height: 1;
    }
}

.counter-section {
    position: relative;
}

.counter-number {
    font-size: 72px;
    line-height: 1;
    margin-bottom: 8px;
    margin-top: -12px;
    font-weight: bold;
    color: $title-color;
}

.counter-text {
    margin-bottom: 0;
}

.counter-box {
    display: flex;

    .counter-icon {
        width: 60px;
        margin-right: 25px;
    }

    .counter-number {
        margin-bottom: 0;
        color: $white-color;
    }

    .counter-text {
        font-size: 18px;
        font-weight: 600;
        color: $white-color;
    }
}

// counter-section-two
.counter-section-two{
    position: relative;
    z-index: -111;
    &::before{
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(135, 201, 13, 0.8);
        z-index: -11;
    }
    &::after{
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(16, 55, 65, 0.20);
        z-index: -1;
    }
    .grass{
        position: absolute;
        top: -59px;
        right: 69px;
        z-index: -111;
    }
}

// counter-box-two
.counter-box-two {
    display: flex;
    align-items: end;
    position: relative;
    z-index: 99;
    .counter-icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        border-radius: 20px;
        background: var(--white-color);
        margin-right: 21px;
    }

    .counter-number {
        margin-bottom: 0;
        color: $white-color;
        font-size: 40px;
        font-weight: 700;
        display: inline;
    }

    .counter-text {
        font-size: 18px;
        font-weight: 600;
        color: $white-color;
        margin-top: 5px;
    }
}

@include lg {
    .counter-number {
        font-size: 60px;
        font-weight: 600;
    }

    .counter-text {
        font-weight: 500;
    }

    .counter-box {
        justify-content: center;
    }
}

@include md {
    .counter-number {
        font-size: 50px;
        margin-bottom: 0;
        margin-top: -7px;
    }
}

@include sm {
    .counter-number {
        font-size: 36px;
        margin-top: -5px;
    }
}