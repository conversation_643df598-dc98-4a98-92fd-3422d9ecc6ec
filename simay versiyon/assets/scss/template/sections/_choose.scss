.choose-tab {
    --border-gap: 4px;
    border: 2px dashed $theme-color;
    border-radius: 50px;
    margin-bottom: 30px;
    height: 62px;
    padding: var(--border-gap);
    display: inline-block;
    position: relative;
    z-index: 2;
    @include xs {
        --border-gap: unset;
    }
    .indicator {
        position: absolute;
        top: var(--border-gap) !important;
        left: 0;
        background-color: $theme-color;
        border-radius: 50px;
        z-index: -1;
        transition: 0.4s;
        @include xs {
            display: none;
        }
    }
}

.choose-content {
    .tab-pane {
        p {
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.choose-btn {
    border: none;
    color: $theme-color;
    background-color: transparent;
    font-size: 18px;
    font-weight: 600;
    border-radius: 50px;
    padding: 12px 30px;
    text-align: center;
    position: relative;
    &:hover {
        color: $secondary-color;
    }

    &.active {
        color: $white-color;

        // &:hover {
        //     color: $white-color;
        //     background-color: $secondary-color;
        // }
    }
}

.long-img {
    height: 100%;

    img {
        height: 100%;
        object-fit: cover;
        object-position: center center;
    }
}

.long-img,
.short-img {
    border-radius: 30px;
    overflow: hidden;

    img {
        border-radius: 30px;
        transition: 0.4s ease-in-out;
    }

    &:hover {
        img {
            border-radius: 30px;
            transform: scale(1.1);
            @include safariNoScale();
        }
    }
}

@include lg {
    .choose-btn {
        padding: 12px 25px;
    }
}

@include xs {
    .why-images {
        --bs-gutter-x: 20px;

        .mb-30 {
            margin-bottom: 20px;
        }
    }

    .choose-btn {
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 400;
    }
}

@include vxs {
    .choose-btn {
        padding: 6px 10px;
        font-size: 14px;
    }
}