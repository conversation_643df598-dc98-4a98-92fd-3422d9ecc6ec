.shape-slider-area {
    position: relative;
    padding-left: 50px;
}

.simple-arrow {
    background-color: transparent;
    border: none;
    @include equal-size(50px);
    color: $title-color;
    font-size: 28px;

    &:hover {
        color: $theme-color;
    }
}

.shape-slider-area {

    .arrow-left,
    .arrow-right {
        position: absolute;
        top: 50%;
        z-index: 3;
    }

    .arrow-left {
        left: 58px;
    }

    .arrow-right {
        top: 57%;
        right: 64px;
    }
}

#sliderOne {
    .slide-item {
        padding-top: 40px;
        position: relative;
    }

    .line-1,
    .line-2 {
        position: absolute;
        top: 0;
    }

    .line-1 {
        left: 40%;
    }

    .line-2 {
        left: 25%;
    }
}

// about 3
.about-form-box {
    padding: 52px 60px 60px 60px;
    border-radius: 30px;

    .form-title,
    .form-text {
        color: $white-color;
    }

    .form-text {
        margin-bottom: 30px;
    }
}

.call-media {
    display: flex;
    align-items: center;
    border-left: 3px solid $theme-color;
    box-shadow: 0 0 30px 3px rgba(0, 0, 0, 0.05);
    padding: 20px 20px 20px 30px;
    margin: 0 0 40px 0;
    max-width: 350px;
    text-align: left;

    .media-icon {
        font-size: 30px;
        color: $theme-color;
        margin-right: 20px;
    }

    .media-label {
        font-size: 16px;
        text-transform: uppercase;
        font-family: $title-font;
        font-weight: 500;
        letter-spacing: 0.02em;
        color: #b3b3b3;
        display: block;
        margin: 0 0 5px 0;
    }

    .media-info {
        font-size: 30px;
        font-family: $body-font;
        margin: 0;
        line-height: 1;
        font-weight: 500;
        color: $title-color;
    }
}

// ==============================
    // about-section
// ==============================

.about-section-two{
    position: relative;
    .tadi{
        position: absolute;
        top: 90px;
        left: 50px;
        @include ml{
            display: none !important;
        }
    }
    .anim-image {
        position: absolute;
        bottom: 165px;
        left: -100px;
    }
    .anim-img{
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: -111;
    }
    .content-box{
        border-radius: 20px;
        background: var(--white-color);
        padding: 57px 59px 51px;
        margin-left: 15px;
        @include lg {
            margin-left: 0;
        }
        @include md{
            padding: 57px 48px 51px;
        }
        @include vxs{
            padding: 57px 20px 51px;
            margin-left: 0;
        }
        .title-area{
            margin-bottom: 18px;
        }
        p{
            margin-bottom: 21px;
        }
        .check-list{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            @include lg{
                justify-content: flex-start;
            }
            @include sm{
                margin-bottom: 25px;
                flex-direction: column;
                align-items: baseline;
            }
            ul{
                @include lg{
                    margin-right: 40px;
                }
                @include sm {
                    margin-right: 0;
                    margin-bottom: 0;
                }
                li{
                    font-size: 18px;
                    color: var(--body-color);
                    padding-left: 27px;
                    margin-bottom: 12px;
                    &::before{
                        width: 18px;
                        height: 18px;
                        line-height: 18px;
                        top: 3px;
                        font-size: 10px;
                    }
                }
            }
        }
    }
    .about-img{
        position: relative;
        padding-right: 15px;
        @include lg {
            padding-right: 0;
        }
        &::before{
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200px;
            height: 200px;
            // background-image: url(../assets/img/normal/about-circle.png);
        }
        img{
            border-radius: 15px;
            @include lg{
                width: 100%;
            }
        }
        .image-one{
            position: relative;
            z-index: 99;
        }
        .image-three{
            @include ml{
                display: none;
            }
        }
        .image{
            position: absolute;
            bottom: 0;
            left: -150px;
            border: 10px solid var(--white-color);
            z-index: 99;
            @include xxl {
                left: -60px;
            }
            @include ml{
                left: -25px;
                bottom: 10px;
                width: 320px;
            }
            @include lg{
                width: unset;
                left: 15px;
            }
            @include xs {
                left: 10px;
            }
            @include vxs{
                display: none;
            }
        }
    }
    .two-btns{
        .vs-btn{
            @include xs{
                width: 100%;
            }
        }
    }
}


@include lg {
    .shape-slider-area .arrow-left {
        left: 0;
    }

    .shape-slider-area .arrow-right {
        right: 0;
    }

    .shape-slider-area {
        max-width: 600px;
        padding-left: 0;
        margin: 0 auto;
    }
}

@include md {
    #sliderOne {
        .slide-item {
            padding-top: 10px;
        }

        .line-2 {
            display: none;
        }
    }

    .call-media {
        margin-left: auto;
        margin-right: auto;
    }
}

@include sm {
    .about-form-box {
        padding: 40px 20px;
        border-radius: 20px;
    }

    .call-media {
        padding: 15px 15px 15px 20px;
        margin: 0 auto 30px auto;

        .media-info {
            font-size: 26px;
        }

        .media-label {
            font-size: 14px;
        }

        .media-icon {
            font-size: 28px;
            margin-right: 15px;
        }
    }
}

@include xs {
    .simple-arrow {
        font-size: 22px;
    }

    .shape-slider-area .arrow-left,
    .shape-slider-area .arrow-right {
        top: 45%;
    }

    .shape-slider-area .arrow-right {
        top: 50%;
    }
}

@include vxs {
    #sliderOne {
        .line-1 {
            display: none;
        }
    }
}