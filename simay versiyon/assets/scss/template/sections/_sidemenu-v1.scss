.sidemenu-wrapper {
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.8s;


  .closeButton {
    display: inline-block;
    border: none;
    width: 40px;
    height: 40px;
    font-size: 18px;
    line-height: 1;
    padding: 0;
    position: absolute;
    top: 20px;
    right: 20px;
    color: $white-color;
    background-color: $theme-color;
    border-radius: 50%;
    transform: rotate(0);
    transition: all ease 0.4s;

    &:hover {
      color: $white-color;
      background-color: $secondary-color;
      transform: rotate(90deg);
    }
  }


  .sidemenu-content {
    background-color: $white-color;
    width: 380px;
    margin-left: auto;
    padding: 50px 40px;
    height: 100%;
    overflow: scroll;
    position: relative;
    right: -500px;
    cursor: auto;
    transition-delay: 1s;
    transition: right ease 1s;

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
      box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
      background-color: #F5F5F5;
    }

    &::-webkit-scrollbar {
      width: 2px;
      background-color: #F5F5F5;
    }

  }

  .widget {
    padding: 0;
    border: none;
    background-color: transparent;
  }


  &.show {
    opacity: 1;
    visibility: visible;
    width: 100%;
    transition: all ease 0.8s;

    .sidemenu-content {
      right: 0;
      opacity: 1;
      visibility: visible;
    }
  }

}