.vs-header {
  position: relative;
}

.header-menu-area {
  position: relative;
  z-index: 3;
}

// Top Header
.header-top-area {
  background-color: $theme-color;
  color: $white-color;
  padding-top: 12px;
  padding-bottom: 12px;
  line-height: 1;

  &.style-2 {
    background-color: $white-color;
    color: $body-color;
    border-bottom: 1px solid rgb(239, 239, 239);
  }
}
.sticky-wrapper.will-sticky{
  position: relative;
  z-index: 999;
  .sticky-active{
    .header-menu-area.v4{
      background-color: var(--white-color);
      border-bottom: 2px solid var(--theme-color);
    }
  }
}

// Top Header
.header-top-area.v4{
  .container{
    max-width: 1250px;
  }
  .header-social{
    ul{
      li{
        margin-right: 10px;
        a{
          background-color: var(--white-color);
          width: 36px;
          height: 36px;
          line-height: 40px;
          border-radius: 50%;
          text-align: center;
          color: var(--theme-color);
          &:hover{
            background-color: var(--secondary-color);
            color: var(--white-color);
          }
        }
      }
    }
  }
}
.header-menu-area.v4{
  background-color: var(--theme-color);
  z-index: revert-layer;
  .container{
    max-width: 1370px;
    margin: 0 auto;
  }
  .lower-header{
    position: relative;
    z-index: 999;
    background-color: var(--white-color);
    padding: 0 60px;
    border-radius: 6px;
    @include md {
      padding: 0 15px;
    }
    .main-menu{
      ul{
        li{
          padding: 32px 0 31px;
          .sub-menu{
            li{
              padding: 3px 9px;
            }
          }
        }
      }
    }
  }
}

.header-links {
  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      display: inline-block;
      position: relative;
      font-weight: 500;
      margin-left: 20px;
      padding-left: 20px;
      font-family: $body-font;

      &:before {
        content: "";
        height: 15px;
        width: 1px;
        position: absolute;
        top: 4px;
        left: 0;
        background-color: rgb(255, 255, 255, 0.6);
      }

      &:first-child {
        margin-left: 0;
        padding-left: 0;

        &:before {
          display: none;
        }
      }

      >i {
        margin-right: 15px;
      }
    }
  }

  display: inline-block;

  a {
    color: $white-color;

    &:hover {
      color: $secondary-color;
    }
  }

  // Style 3
  &.style-3 {
    ul {
      display: flex;
      align-items: center;
    }

    li {
      &:before {
        top: 0;
      }
    }
  }
}

.header-notice {
  display: flex;
  align-items: center;

  .date {
    margin-right: 20px;
    padding-right: 20px;
    position: relative;

    &:after {
      content: "";
      height: 20px;
      width: 1px;
      position: absolute;
      top: 5px;
      right: 0;
      background-color: rgba(255, 255, 255, 0.8);
    }
  }

  .day {
    font-size: 30px;
    font-weight: bold;
    margin-right: 11px;
  }

  .month {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 400;
    width: 25px;
    display: inline-block;
    line-height: 1.1;
    transform: translateY(2px);
  }
}

.header-info {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.4;

  a {
    font-size: inherit;
    color: $title-color;
    display: block;

    &:hover {
      color: $theme-color;
    }
  }
}

.header-button {
  display: flex;
  align-items: center;

  .vs-btn,
  button {
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }

  .simple-icon {
    margin-right: 30px;

    &:last-child {
      margin-right: 0;
    }
  }
}


// Close Btn
.vs-menu-wrapper .vs-menu-toggle {
  right: -20px;
  top: 40px;
  @include equal-size(40px);
  line-height: 42px;
  font-size: 18px;
  text-align: center;

  i {
    margin-right: 0;
    line-height: 1.6;
  }
}

.vs-menu-toggle {
  height: 40px;
  width: auto;
  padding: 6px 20px 5px 20px;
  border-radius: 50px;
  line-height: 1;
  font-size: 16px;

  i {
    margin-right: 5px;
    vertical-align: middle;
    position: relative;
    top: 0;
  }

  &.simple-icon {
    background-color: transparent;
    color: $title-color;
    padding: 0;
  }
}

.will-sticky {
  .sticky-active {
    position: fixed;
    top: -100%;
    right: 0;
    left: 0;
    background-color: $white-color;
    transition: all ease 0.8s;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.07);
    z-index: 20;

    &.active {
      top: 0;
    }
  }
}


// Header menu
.header-logo {
  padding: 10px 0;
}

.main-menu {
  a {
    display: block;
    position: relative;
    font-family: $body-font;
    font-weight: 500;
    font-size: 18px;
    color: $title-color;

    &:hover {
      color: $theme-color;
    }
  }

  >ul {
    >li {
      padding: 36px 0;

      >a {
        padding: 5px 13px;
        border-radius: 20px;

        &:hover {
          background-color: $theme-color;
          color: $white-color;
        }
      }
    }
  }

  ul {
    margin: 0;
    padding: 0;

    li {
      list-style-type: none;
      display: inline-block;
      position: relative;

      &.menu-item-has-children {
        >a {
          &:after {
            content: "\f107";
            position: relative;
            font-family: $icon-font;
            margin-left: 3px;
            top: 0.8px;
            font-size: 0.8rem;
          }
        }
      }

      &:last-child {
        margin-right: 0;
      }

      &:first-child {
        margin-left: 0;
      }

      &:hover {

        >ul.sub-menu,
        >ul.mega-menu {
          visibility: visible;
          opacity: 1;
          margin-top: 0;
          z-index: 9;
        }
      }
    }
  }

  ul.sub-menu,
  ul.mega-menu {
    position: absolute;
    text-align: left;
    top: 100%;
    left: 0;
    background-color: $white-color;
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    visibility: hidden;
    min-width: 190px;
    width: max-content;
    padding: 7px;
    left: -14px;
    margin-top: 50px;
    opacity: 0;
    z-index: -1;
    border-bottom: 3px solid $theme-color;
    box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.09), 0px 3px 0px 0px rgba(231, 13, 60, 0.004);
    transform-origin: top center;
    transition: margin-top 0.4s ease-in-out 0s, visibility 0.4s ease-in-out 0s, opacity 0.4s ease-in-out 0s, z-index 0s;
  }

  ul.sub-menu {
    padding: 18px 20px;
    left: -27px;

    &:before {
      content: "";
      position: absolute;
      left: 34.5px;
      top: 30px;
      width: 1px;
      background-color: #ededed;
      height: calc(100% - 60px);
    }

    li {
      display: block;
      margin: 0 0;
      padding: 3px 9px;

      &.menu-item-has-children {
        >a:after {
          content: "\f105";
          float: right;
          top: 0;
        }
      }

      a {
        position: relative;
        padding-left: 21px;

        &:before {
          content: "\f111";
          position: absolute;
          top: 2.0em;
          left: 0;
          font-family: $icon-font;
          width: 11px;
          height: 11px;
          text-align: center;
          border-radius: 50%;
          display: inline-block;
          font-size: 0.18em;
          line-height: 11px;
          color: $theme-color;
          font-weight: 700;
          background-color: $white-color;
          box-shadow: inset 0px 2px 4px 0px rgba(#ad8858, 0.4);
        }

        &:hover {
          color: $theme-color;
        }
      }

      ul.sub-menu {
        left: 100%;
        right: auto;
        top: 0;
        margin: 0 0;
        margin-left: 20px;

        li {
          ul {
            left: 100%;
            right: auto;
          }
        }
      }
    }
  }

  .mega-menu-wrap {
    position: static;
  }

  ul.mega-menu {
    display: flex;
    justify-content: space-between;
    text-align: left;
    width: 100%;
    max-width: var(--main-container);
    padding: 20px 15px 23px 15px;
    left: 50%;
    transform: translateX(-50%);

    li {
      display: block;
      width: 100%;
      padding: 0 15px;

      li {
        padding: 4px 0;
      }

      a {
        display: inline-block;
      }
    }

    >li {
      >a {
        display: block;
        padding: 0;
        padding-bottom: 15px;
        margin-bottom: 10px;
        text-transform: capitalize;
        letter-spacing: 1px;
        font-weight: 700;
        color: $title-color;
        border-color: $theme-color;

        &::after,
        &::before {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 15px;
          height: 1px;
          background-color: $theme-color;
        }

        &::after {
          width: calc(100% - 20px);
          left: 20px;
        }

        &:hover {
          padding-left: 0;
        }
      }
    }
  }
}

.menu-style2 {
  >ul {
    >li {
      padding: 0;
      margin: 0 17px;

      >a {
        padding: 20px 0;

        &:hover {
          background-color: transparent;
          color: $theme-color;
        }
      }
    }
  }
}

.header-social {
  ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
  }

  li {
    display: inline-block;
    margin: 0 20px 0 0;

    &:last-child {
      margin-right: 0;
    }
  }

  a {
    display: block;
    color: $white-color;

    &:hover {
      color: $secondary-color;
    }
  }
}

@include ml {
  .main-menu>ul>li>a {
    padding: 5px 11px;
  }
}

@include lg {
  .header-links {
    ul {
      li {
        margin-left: 15px;
        padding-left: 15px;
      }
    }
  }
}



@include vxs {
  .header-logo {
    max-width: 140px;
  }
}

@include md {
  .will-sticky .header-menu-area.v4 .lower-header {
    padding: 0;
  }
}