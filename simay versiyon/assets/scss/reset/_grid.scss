.slick-track>[class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x)/ 2);
  padding-left: calc(var(--bs-gutter-x)/ 2);
  margin-top: var(--bs-gutter-y);
}

@media (min-width: $lg) {
  .row {
    --bs-gutter-x: 30px;
  }
}

.gy-30 {
  --bs-gutter-y: 30px;
}

.g-0 {
  --bs-gutter-x: 0px;
  --bs-gutter-y: 0px;
}

.gx-15 {
  --bs-gutter-x: 15px;
}

.gx-20,
.g-20 {
  --bs-gutter-x: 20px;
}

.gy-20,
.g-20 {
  --bs-gutter-y: 20px;
}

.gx-40 {
  --bs-gutter-x: 40px;
}

.gy-40 {
  --bs-gutter-y: 40px;
}

.gx-60 {
  --bs-gutter-x: 60px;
}

.gy-60 {
  --bs-gutter-y: 60px;
}

.gx-70 {
  --bs-gutter-x: 70px;
}

.gy-70 {
  --bs-gutter-y: 70px;
}

.gx-150 {
  --bs-gutter-x: 150px;
}

@media (min-width: $ml) {
  .gx-30 {
    --bs-gutter-x: 30px;
  }
}

@include lg {
  .gx-150 {
    --bs-gutter-x: 30px;
  }

  .gx-60 {
    --bs-gutter-x: 40px;
  }

  .gx-70 {
    --bs-gutter-x: 30px;
  }

  .gx-40 {
    --bs-gutter-x: 30px;
  }

  .gy-40 {
    --bs-gutter-y: 30px;
  }

  .gx-lg-15 {
    --bs-gutter-x: 15px;
  }
}